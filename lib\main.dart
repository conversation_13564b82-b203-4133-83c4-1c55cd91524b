import 'package:flutter/material.dart';
// import 'constants/app_theme.dart'; // للتبديل للتصميم القديم عند الحاجة
import 'constants/revolutionary_theme.dart';
import 'constants/app_constants.dart';
import 'screens/splash_screen.dart';
import 'screens/revolutionary_home_screen.dart';
import 'screens/revolutionary_demo_screen.dart';
import 'widgets/keyboard_shortcuts_wrapper.dart';

void main() {
  runApp(const SmartLedgerApp());
}

class SmartLedgerApp extends StatelessWidget {
  const SmartLedgerApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: AppConstants.appName,
      // استخدام التصميم الثوري الجديد
      theme: RevolutionaryTheme.revolutionaryTheme,
      // يمكن التبديل بين التصميم القديم والجديد
      // theme: AppTheme.lightTheme,

      // الصفحة الرئيسية الثورية
      home: const KeyboardShortcutsWrapper(
        screenType: 'global',
        child: RevolutionaryHomeScreen(),
      ),

      // للعرض التوضيحي، يمكن استخدام:
      // home: const RevolutionaryDemoScreen(),
      debugShowCheckedModeBanner: false,
      locale: const Locale('ar', 'SY'),
      builder: (context, child) {
        return Directionality(textDirection: TextDirection.rtl, child: child!);
      },

      // إضافة مسارات للصفحات الجديدة
      routes: {
        '/home': (context) => const RevolutionaryHomeScreen(),
        '/demo': (context) => const RevolutionaryDemoScreen(),
        '/splash': (context) => const SplashScreen(),
      },
    );
  }
}
