/// نموذج موقع المستودع
/// يمثل موقع محدد داخل المستودع لتخزين الأصناف
library;

class WarehouseLocation {
  final int? id;
  final String code;
  final String name;
  final String? description;
  final int warehouseId;
  final String? zone; // منطقة داخل المستودع
  final String? aisle; // ممر
  final String? shelf; // رف
  final String? bin; // صندوق/خانة
  final double? maxCapacity; // السعة القصوى
  final String? locationBarcode; // باركود الموقع
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  WarehouseLocation({
    this.id,
    required this.code,
    required this.name,
    this.description,
    required this.warehouseId,
    this.zone,
    this.aisle,
    this.shelf,
    this.bin,
    this.maxCapacity,
    this.locationBarcode,
    this.isActive = true,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// تحويل إلى Map لحفظ في قاعدة البيانات
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'code': code,
      'name': name,
      'description': description,
      'warehouse_id': warehouseId,
      'zone': zone,
      'aisle': aisle,
      'shelf': shelf,
      'bin': bin,
      'max_capacity': maxCapacity,
      'location_barcode': locationBarcode,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// إنشاء من Map
  factory WarehouseLocation.fromMap(Map<String, dynamic> map) {
    return WarehouseLocation(
      id: map['id']?.toInt(),
      code: map['code'] ?? '',
      name: map['name'] ?? '',
      description: map['description'],
      warehouseId: map['warehouse_id']?.toInt() ?? 0,
      zone: map['zone'],
      aisle: map['aisle'],
      shelf: map['shelf'],
      bin: map['bin'],
      maxCapacity: map['max_capacity']?.toDouble(),
      locationBarcode: map['location_barcode'],
      isActive: (map['is_active'] ?? 1) == 1,
      createdAt: DateTime.parse(
        map['created_at'] ?? DateTime.now().toIso8601String(),
      ),
      updatedAt: DateTime.parse(
        map['updated_at'] ?? DateTime.now().toIso8601String(),
      ),
    );
  }

  /// نسخ مع تعديل
  WarehouseLocation copyWith({
    int? id,
    String? code,
    String? name,
    String? description,
    int? warehouseId,
    String? zone,
    String? aisle,
    String? shelf,
    String? bin,
    double? maxCapacity,
    String? locationBarcode,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return WarehouseLocation(
      id: id ?? this.id,
      code: code ?? this.code,
      name: name ?? this.name,
      description: description ?? this.description,
      warehouseId: warehouseId ?? this.warehouseId,
      zone: zone ?? this.zone,
      aisle: aisle ?? this.aisle,
      shelf: shelf ?? this.shelf,
      bin: bin ?? this.bin,
      maxCapacity: maxCapacity ?? this.maxCapacity,
      locationBarcode: locationBarcode ?? this.locationBarcode,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'WarehouseLocation(id: $id, code: $code, name: $name, warehouseId: $warehouseId)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is WarehouseLocation && other.id == id;
  }

  @override
  int get hashCode {
    return id.hashCode;
  }

  // خصائص مساعدة
  String get fullLocationCode {
    final parts = <String>[];
    if (zone != null) parts.add(zone!);
    if (aisle != null) parts.add(aisle!);
    if (shelf != null) parts.add(shelf!);
    if (bin != null) parts.add(bin!);
    return parts.isNotEmpty ? '$code-${parts.join('-')}' : code;
  }

  String get displayName {
    return description?.isNotEmpty == true ? '$name ($description)' : name;
  }

  bool get hasCapacityLimit {
    return maxCapacity != null && maxCapacity! > 0;
  }

  /// التحقق من توفر مساحة
  bool canAccommodate(double quantity) {
    if (!hasCapacityLimit) return true;
    return quantity <= maxCapacity!;
  }
}
