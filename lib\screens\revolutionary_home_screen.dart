import 'package:flutter/material.dart';
import '../constants/revolutionary_design_colors.dart';
import '../widgets/revolutionary_ui_components.dart';


/// الشاشة الرئيسية الثورية لـ Smart Ledger
class RevolutionaryHomeScreen extends StatefulWidget {
  const RevolutionaryHomeScreen({super.key});

  @override
  State<RevolutionaryHomeScreen> createState() => _RevolutionaryHomeScreenState();
}

class _RevolutionaryHomeScreenState extends State<RevolutionaryHomeScreen>
    with TickerProviderStateMixin {
  late AnimationController _headerAnimationController;
  late AnimationController _cardsAnimationController;
  late Animation<double> _headerAnimation;
  late Animation<double> _cardsAnimation;
  
  int _selectedNavIndex = 0;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _startAnimations();
  }

  void _setupAnimations() {
    _headerAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    
    _cardsAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _headerAnimation = CurvedAnimation(
      parent: _headerAnimationController,
      curve: Curves.elasticOut,
    );

    _cardsAnimation = CurvedAnimation(
      parent: _cardsAnimationController,
      curve: Curves.elasticOut,
    );
  }

  void _startAnimations() {
    _headerAnimationController.forward();
    Future.delayed(const Duration(milliseconds: 300), () {
      _cardsAnimationController.forward();
    });
  }

  @override
  void dispose() {
    _headerAnimationController.dispose();
    _cardsAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: RevolutionaryColors.jasmineGradient,
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildRevolutionaryHeader(),
              Expanded(
                child: _buildMainContent(),
              ),
            ],
          ),
        ),
      ),
      bottomNavigationBar: _buildFloatingNavBar(),
    );
  }

  /// رأس الصفحة الثوري
  Widget _buildRevolutionaryHeader() {
    return AnimatedBuilder(
      animation: _headerAnimation,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, -50 * (1 - _headerAnimation.value)),
          child: Opacity(
            opacity: _headerAnimation.value,
            child: Container(
              padding: const EdgeInsets.all(20),
              decoration: const BoxDecoration(
                gradient: RevolutionaryColors.damascusGradient,
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(30),
                  bottomRight: Radius.circular(30),
                ),
                boxShadow: [
                  BoxShadow(
                    color: RevolutionaryColors.shadowMedium,
                    blurRadius: 15,
                    offset: Offset(0, 5),
                  ),
                ],
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: RevolutionaryColors.syrianGold.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(15),
                        ),
                        child: const Icon(
                          Icons.account_balance,
                          color: RevolutionaryColors.syrianGold,
                          size: 32,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Smart Ledger',
                              style: TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                color: RevolutionaryColors.textOnDark,
                                fontFamily: 'Cairo',
                              ),
                            ),
                            Text(
                              'دفتر الحسابات الذكي',
                              style: TextStyle(
                                fontSize: 14,
                                color: RevolutionaryColors.textOnDark.withValues(alpha: 0.8),
                                fontFamily: 'Cairo',
                              ),
                            ),
                          ],
                        ),
                      ),
                      IconButton(
                        onPressed: () {},
                        icon: const Icon(
                          Icons.notifications_outlined,
                          color: RevolutionaryColors.syrianGold,
                          size: 28,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  _buildQuickStats(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// إحصائيات سريعة
  Widget _buildQuickStats() {
    return Row(
      children: [
        Expanded(
          child: _buildQuickStatItem(
            'إجمالي الأرباح',
            '₺ 125,430',
            Icons.trending_up,
            RevolutionaryColors.successGlow,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildQuickStatItem(
            'المبيعات اليوم',
            '₺ 8,250',
            Icons.point_of_sale,
            RevolutionaryColors.syrianGold,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildQuickStatItem(
            'العملاء النشطين',
            '342',
            Icons.people,
            RevolutionaryColors.infoTurquoise,
          ),
        ),
      ],
    );
  }

  Widget _buildQuickStatItem(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: RevolutionaryColors.jasmineWhite.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
              fontFamily: 'Cairo',
            ),
          ),
          Text(
            title,
            style: TextStyle(
              fontSize: 10,
              color: RevolutionaryColors.textOnDark.withValues(alpha: 0.7),
              fontFamily: 'Cairo',
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// المحتوى الرئيسي
  Widget _buildMainContent() {
    return AnimatedBuilder(
      animation: _cardsAnimation,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, 30 * (1 - _cardsAnimation.value)),
          child: Opacity(
            opacity: _cardsAnimation.value,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'الوحدات الرئيسية',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: RevolutionaryColors.textPrimary,
                      fontFamily: 'Cairo',
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildMainModulesGrid(),
                  const SizedBox(height: 24),
                  const Text(
                    'الإحصائيات المتقدمة',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: RevolutionaryColors.textPrimary,
                      fontFamily: 'Cairo',
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildAdvancedStats(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// شبكة الوحدات الرئيسية
  Widget _buildMainModulesGrid() {
    final modules = [
      ModuleItem('دليل الحسابات', Icons.account_tree, RevolutionaryColors.damascusSky),
      ModuleItem('الفواتير', Icons.description, RevolutionaryColors.syrianGold),
      ModuleItem('المخزون', Icons.inventory, RevolutionaryColors.oliveBranch),
      ModuleItem('التقارير', Icons.analytics, RevolutionaryColors.errorCoral),
      ModuleItem('العملاء', Icons.people, RevolutionaryColors.infoTurquoise),
      ModuleItem('الإعدادات', Icons.settings, RevolutionaryColors.textSecondary),
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 1.2,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      itemCount: modules.length,
      itemBuilder: (context, index) {
        final module = modules[index];
        return RevolutionaryUI.floating3DCard(
          title: module.title,
          subtitle: 'إدارة ${module.title}',
          icon: module.icon,
          accentColor: module.color,
          onTap: () => _navigateToModule(module.title),
          child: const SizedBox.shrink(),
        );
      },
    );
  }

  /// الإحصائيات المتقدمة
  Widget _buildAdvancedStats() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: RevolutionaryUI.animatedStatsCard(
                title: 'إجمالي المبيعات',
                value: '₺ 1,234,567',
                icon: Icons.trending_up,
                color: RevolutionaryColors.successGlow,
                percentage: 12.5,
                isIncreasing: true,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: RevolutionaryUI.animatedStatsCard(
                title: 'المصروفات',
                value: '₺ 456,789',
                icon: Icons.trending_down,
                color: RevolutionaryColors.errorCoral,
                percentage: 3.2,
                isIncreasing: false,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// شريط التنقل العائم
  Widget _buildFloatingNavBar() {
    final navItems = [
      NavigationItem(icon: Icons.home, label: 'الرئيسية'),
      NavigationItem(icon: Icons.analytics, label: 'التقارير'),
      NavigationItem(icon: Icons.inventory, label: 'المخزون'),
      NavigationItem(icon: Icons.people, label: 'العملاء'),
      NavigationItem(icon: Icons.settings, label: 'الإعدادات'),
    ];

    return RevolutionaryUI.floatingNavigationBar(
      items: navItems,
      currentIndex: _selectedNavIndex,
      onTap: (index) {
        setState(() {
          _selectedNavIndex = index;
        });
      },
    );
  }

  void _navigateToModule(String moduleName) {
    // التنقل إلى الوحدة المحددة
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('الانتقال إلى $moduleName'),
        backgroundColor: RevolutionaryColors.damascusSky,
      ),
    );
  }
}

/// عنصر الوحدة
class ModuleItem {
  final String title;
  final IconData icon;
  final Color color;

  ModuleItem(this.title, this.icon, this.color);
}
