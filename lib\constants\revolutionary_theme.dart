import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'revolutionary_design_colors.dart';

/// نظام ثيم ثوري ومبتكر لـ Smart Ledger
class RevolutionaryTheme {
  /// الثيم الرئيسي الثوري
  static ThemeData get revolutionaryTheme {
    return ThemeData(
      useMaterial3: true,

      // نظام الألوان الثوري
      colorScheme: ColorScheme.fromSeed(
        seedColor: RevolutionaryColors.damascusSky,
        brightness: Brightness.light,
        primary: RevolutionaryColors.damascusSky,
        onPrimary: RevolutionaryColors.textOnDark,
        primaryContainer: RevolutionaryColors.jasminePetal,
        secondary: RevolutionaryColors.syrianGold,
        onSecondary: RevolutionaryColors.textOnGold,
        secondaryContainer: RevolutionaryColors.desertSand,
        surface: RevolutionaryColors.backgroundCard,
        onSurface: RevolutionaryColors.textPrimary,
        surfaceContainerHighest: RevolutionaryColors.backgroundSecondary,
        error: RevolutionaryColors.errorCoral,
        onError: RevolutionaryColors.textOnDark,
        outline: RevolutionaryColors.borderMedium,
        shadow: RevolutionaryColors.shadowMedium,
      ),

      // خط عربي فاخر
      fontFamily: 'Cairo',

      // شريط التطبيق الثوري
      appBarTheme: AppBarTheme(
        backgroundColor: Colors.transparent,
        foregroundColor: RevolutionaryColors.textOnDark,
        elevation: 0,
        centerTitle: true,
        systemOverlayStyle: SystemUiOverlayStyle.light,
        titleTextStyle: const TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.w800,
          color: RevolutionaryColors.textOnDark,
          fontFamily: 'Cairo',
          letterSpacing: 0.5,
        ),
        iconTheme: const IconThemeData(
          color: RevolutionaryColors.textOnDark,
          size: 28,
        ),
        actionsIconTheme: const IconThemeData(
          color: RevolutionaryColors.syrianGold,
          size: 26,
        ),
      ),

      // البطاقات الثورية
      cardTheme: CardThemeData(
        color: RevolutionaryColors.backgroundCard,
        elevation: 8,
        shadowColor: RevolutionaryColors.shadowBlue,
        surfaceTintColor: RevolutionaryColors.jasminePetal,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
          side: const BorderSide(
            color: RevolutionaryColors.borderLight,
            width: 1,
          ),
        ),
        margin: const EdgeInsets.all(8),
        clipBehavior: Clip.antiAlias,
      ),

      // الأزرار الثورية
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: RevolutionaryColors.damascusSky,
          foregroundColor: RevolutionaryColors.textOnDark,
          elevation: 8,
          shadowColor: RevolutionaryColors.shadowBlue,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(25),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
          textStyle: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            fontFamily: 'Cairo',
          ),
        ),
      ),

      // أزرار النص
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: RevolutionaryColors.damascusSky,
          textStyle: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            fontFamily: 'Cairo',
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15),
          ),
        ),
      ),

      // الأزرار المحددة
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: RevolutionaryColors.damascusSky,
          side: const BorderSide(
            color: RevolutionaryColors.damascusSky,
            width: 2,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
          textStyle: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            fontFamily: 'Cairo',
          ),
        ),
      ),

      // حقول الإدخال الثورية
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: RevolutionaryColors.backgroundCard,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: const BorderSide(
            color: RevolutionaryColors.borderLight,
            width: 1.5,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: const BorderSide(
            color: RevolutionaryColors.borderMedium,
            width: 1.5,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: const BorderSide(
            color: RevolutionaryColors.damascusSky,
            width: 2,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: const BorderSide(
            color: RevolutionaryColors.errorCoral,
            width: 2,
          ),
        ),
        contentPadding: const EdgeInsets.all(20),
        labelStyle: const TextStyle(
          color: RevolutionaryColors.textSecondary,
          fontFamily: 'Cairo',
          fontWeight: FontWeight.w500,
        ),
        hintStyle: const TextStyle(
          color: RevolutionaryColors.textHint,
          fontFamily: 'Cairo',
        ),
        prefixIconColor: RevolutionaryColors.damascusSky,
        suffixIconColor: RevolutionaryColors.damascusSky,
      ),

      // شريط التنقل السفلي
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: RevolutionaryColors.backgroundCard,
        selectedItemColor: RevolutionaryColors.damascusSky,
        unselectedItemColor: RevolutionaryColors.textSecondary,
        selectedLabelStyle: TextStyle(
          fontFamily: 'Cairo',
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: TextStyle(
          fontFamily: 'Cairo',
          fontWeight: FontWeight.w500,
        ),
        type: BottomNavigationBarType.fixed,
        elevation: 8,
      ),

      // الحوارات
      dialogTheme: DialogThemeData(
        backgroundColor: RevolutionaryColors.backgroundCard,
        elevation: 16,
        shadowColor: RevolutionaryColors.shadowMedium,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(25)),
        titleTextStyle: const TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: RevolutionaryColors.textPrimary,
          fontFamily: 'Cairo',
        ),
        contentTextStyle: const TextStyle(
          fontSize: 16,
          color: RevolutionaryColors.textSecondary,
          fontFamily: 'Cairo',
        ),
      ),

      // القوائم المنسدلة
      dropdownMenuTheme: DropdownMenuThemeData(
        textStyle: const TextStyle(
          fontSize: 16,
          color: RevolutionaryColors.textPrimary,
          fontFamily: 'Cairo',
        ),
        menuStyle: MenuStyle(
          backgroundColor: WidgetStateProperty.all(
            RevolutionaryColors.backgroundCard,
          ),
          elevation: WidgetStateProperty.all(8),
          shape: WidgetStateProperty.all(
            RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          ),
        ),
      ),

      // أشرطة التمرير
      scrollbarTheme: ScrollbarThemeData(
        thumbColor: WidgetStateProperty.all(
          RevolutionaryColors.damascusSky.withValues(alpha: 0.6),
        ),
        trackColor: WidgetStateProperty.all(RevolutionaryColors.borderLight),
        radius: const Radius.circular(8),
        thickness: WidgetStateProperty.all(8),
      ),

      // مؤشرات التقدم
      progressIndicatorTheme: const ProgressIndicatorThemeData(
        color: RevolutionaryColors.damascusSky,
        linearTrackColor: RevolutionaryColors.borderLight,
        circularTrackColor: RevolutionaryColors.borderLight,
      ),

      // الرقائق (Chips)
      chipTheme: ChipThemeData(
        backgroundColor: RevolutionaryColors.jasminePetal,
        selectedColor: RevolutionaryColors.damascusSky,
        labelStyle: const TextStyle(
          color: RevolutionaryColors.textPrimary,
          fontFamily: 'Cairo',
        ),
        secondaryLabelStyle: const TextStyle(
          color: RevolutionaryColors.textOnDark,
          fontFamily: 'Cairo',
        ),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        elevation: 4,
        shadowColor: RevolutionaryColors.shadowSoft,
      ),

      // التبديل (Switch)
      switchTheme: SwitchThemeData(
        thumbColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return RevolutionaryColors.syrianGold;
          }
          return RevolutionaryColors.textSecondary;
        }),
        trackColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return RevolutionaryColors.damascusSky.withValues(alpha: 0.5);
          }
          return RevolutionaryColors.borderMedium;
        }),
      ),

      // مربعات الاختيار
      checkboxTheme: CheckboxThemeData(
        fillColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return RevolutionaryColors.damascusSky;
          }
          return RevolutionaryColors.backgroundCard;
        }),
        checkColor: WidgetStateProperty.all(RevolutionaryColors.textOnDark),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
      ),

      // أزرار الراديو
      radioTheme: RadioThemeData(
        fillColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return RevolutionaryColors.damascusSky;
          }
          return RevolutionaryColors.textSecondary;
        }),
      ),
    );
  }

  /// ثيم داكن ثوري (للاستخدام المستقبلي)
  static ThemeData get revolutionaryDarkTheme {
    return revolutionaryTheme.copyWith(
      brightness: Brightness.dark,
      colorScheme: ColorScheme.fromSeed(
        seedColor: RevolutionaryColors.damascusSky,
        brightness: Brightness.dark,
        primary: RevolutionaryColors.damascusSkyLight,
        surface: RevolutionaryColors.arabicCalligraphy,
        onSurface: RevolutionaryColors.jasmineWhite,
      ),
    );
  }
}
