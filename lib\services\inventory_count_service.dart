/// خدمة إدارة الجرد الدوري
/// تدير جلسات الجرد المجدولة والمنظمة مع تتبع الفروقات
library;

import '../database/database_helper.dart';
import '../models/inventory_count.dart';

import '../models/inventory_movement.dart';
import '../services/logging_service.dart';
import '../services/audit_service.dart';
import '../services/item_location_stock_service.dart';
import '../exceptions/validation_exception.dart';

class InventoryCountService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final ItemLocationStockService _stockService = ItemLocationStockService();

  /// إنشاء جداول الجرد
  Future<void> createTables() async {
    final db = await _databaseHelper.database;

    await db.transaction((txn) async {
      // جدول جلسات الجرد
      await txn.execute('''
        CREATE TABLE IF NOT EXISTS inventory_counts (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          count_number TEXT NOT NULL UNIQUE,
          title TEXT NOT NULL,
          description TEXT,
          count_type TEXT NOT NULL,
          status TEXT NOT NULL,
          warehouse_id INTEGER,
          location_id INTEGER,
          scheduled_date TEXT NOT NULL,
          started_at TEXT,
          completed_at TEXT,
          counted_by TEXT,
          approved_by TEXT,
          approved_at TEXT,
          settings TEXT,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL,
          FOREIGN KEY (warehouse_id) REFERENCES warehouses (id),
          FOREIGN KEY (location_id) REFERENCES warehouse_locations (id)
        )
      ''');

      // جدول عناصر الجرد
      await txn.execute('''
        CREATE TABLE IF NOT EXISTS inventory_count_items (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          count_id INTEGER NOT NULL,
          item_id INTEGER NOT NULL,
          location_id INTEGER NOT NULL,
          system_quantity REAL NOT NULL,
          counted_quantity REAL,
          variance REAL,
          notes TEXT,
          is_recounted INTEGER NOT NULL DEFAULT 0,
          counted_at TEXT,
          counted_by TEXT,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL,
          FOREIGN KEY (count_id) REFERENCES inventory_counts (id),
          FOREIGN KEY (item_id) REFERENCES items (id),
          FOREIGN KEY (location_id) REFERENCES warehouse_locations (id),
          UNIQUE(count_id, item_id, location_id)
        )
      ''');

      // إنشاء فهارس للأداء
      await txn.execute(
        'CREATE INDEX IF NOT EXISTS idx_inventory_counts_number ON inventory_counts(count_number)',
      );
      await txn.execute(
        'CREATE INDEX IF NOT EXISTS idx_inventory_counts_status ON inventory_counts(status)',
      );
      await txn.execute(
        'CREATE INDEX IF NOT EXISTS idx_inventory_counts_scheduled ON inventory_counts(scheduled_date)',
      );
      await txn.execute(
        'CREATE INDEX IF NOT EXISTS idx_inventory_count_items_count_id ON inventory_count_items(count_id)',
      );
      await txn.execute(
        'CREATE INDEX IF NOT EXISTS idx_inventory_count_items_item_id ON inventory_count_items(item_id)',
      );
    });

    LoggingService.info(
      'تم إنشاء جداول الجرد',
      category: 'InventoryCountService',
    );
  }

  /// إنشاء جلسة جرد جديدة
  Future<int> createInventoryCount(InventoryCount count) async {
    try {
      await _validateInventoryCount(count);

      final db = await _databaseHelper.database;

      // التأكد من وجود الجداول
      await createTables();

      final id = await db.insert('inventory_counts', count.toMap());

      await AuditService.logCreate(
        entityType: 'inventory_count',
        entityId: id,
        entityName: count.title,
        newValues: count.toMap(),
        description: 'تم إنشاء جلسة جرد جديدة: ${count.title}',
        category: 'Inventory',
      );

      LoggingService.info(
        'تم إنشاء جلسة جرد جديدة',
        category: 'InventoryCountService',
        data: {
          'count_id': id,
          'count_number': count.countNumber,
          'count_type': count.countType.code,
        },
      );

      return id;
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء جلسة الجرد',
        category: 'InventoryCountService',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// بدء جلسة الجرد
  Future<void> startInventoryCount(int countId, String countedBy) async {
    try {
      final count = await getInventoryCountById(countId);
      if (count == null) {
        throw ValidationException('جلسة الجرد غير موجودة');
      }

      if (!count.canStart) {
        throw ValidationException('لا يمكن بدء هذه الجلسة في الحالة الحالية');
      }

      final db = await _databaseHelper.database;

      await db.transaction((txn) async {
        // تحديث حالة الجلسة
        final updatedCount = count.copyWith(
          status: InventoryCountStatus.inProgress,
          startedAt: DateTime.now(),
          countedBy: countedBy,
          updatedAt: DateTime.now(),
        );

        await txn.update(
          'inventory_counts',
          updatedCount.toMap(),
          where: 'id = ?',
          whereArgs: [countId],
        );

        // إنشاء عناصر الجرد من المخزون الحالي
        await _generateCountItems(txn, count);
      });

      await AuditService.log(
        action: 'inventory_count_started',
        entityType: 'inventory_count',
        entityId: countId,
        description: 'تم بدء جلسة الجرد بواسطة $countedBy',
        category: 'Inventory',
      );

      LoggingService.info(
        'تم بدء جلسة الجرد',
        category: 'InventoryCountService',
        data: {'count_id': countId, 'counted_by': countedBy},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في بدء جلسة الجرد',
        category: 'InventoryCountService',
        data: {'count_id': countId, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// تحديث كمية عنصر في الجرد
  Future<void> updateCountItem({
    required int countId,
    required int itemId,
    required int locationId,
    required double countedQuantity,
    String? notes,
    String? countedBy,
  }) async {
    try {
      final db = await _databaseHelper.database;

      // الحصول على العنصر الحالي
      final result = await db.query(
        'inventory_count_items',
        where: 'count_id = ? AND item_id = ? AND location_id = ?',
        whereArgs: [countId, itemId, locationId],
      );

      if (result.isEmpty) {
        throw ValidationException('عنصر الجرد غير موجود');
      }

      final currentItem = InventoryCountItem.fromMap(result.first);
      final updatedItem = currentItem
          .updateCountedQuantity(countedQuantity)
          .copyWith(notes: notes, countedBy: countedBy);

      await db.update(
        'inventory_count_items',
        updatedItem.toMap(),
        where: 'id = ?',
        whereArgs: [currentItem.id],
      );

      await AuditService.log(
        action: 'count_item_updated',
        entityType: 'inventory_count_item',
        entityId: currentItem.id!,
        description: 'تم تحديث عنصر الجرد',
        category: 'Inventory',
      );

      LoggingService.info(
        'تم تحديث عنصر الجرد',
        category: 'InventoryCountService',
        data: {
          'count_id': countId,
          'item_id': itemId,
          'location_id': locationId,
          'counted_quantity': countedQuantity,
          'variance': updatedItem.variance,
        },
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث عنصر الجرد',
        category: 'InventoryCountService',
        data: {'count_id': countId, 'item_id': itemId, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// إكمال جلسة الجرد
  Future<void> completeInventoryCount(int countId) async {
    try {
      final count = await getInventoryCountById(countId);
      if (count == null) {
        throw ValidationException('جلسة الجرد غير موجودة');
      }

      if (!count.canComplete) {
        throw ValidationException('لا يمكن إكمال هذه الجلسة في الحالة الحالية');
      }

      final db = await _databaseHelper.database;

      // التحقق من أن جميع العناصر تم عدها
      final uncountedItems = await db.rawQuery(
        '''
        SELECT COUNT(*) as count
        FROM inventory_count_items
        WHERE count_id = ? AND counted_quantity IS NULL
      ''',
        [countId],
      );

      final uncountedCount = (uncountedItems.first['count'] as int?) ?? 0;
      if (uncountedCount > 0) {
        throw ValidationException('يوجد $uncountedCount عنصر لم يتم عده بعد');
      }

      // تحديث حالة الجلسة
      final updatedCount = count.copyWith(
        status: InventoryCountStatus.completed,
        completedAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await db.update(
        'inventory_counts',
        updatedCount.toMap(),
        where: 'id = ?',
        whereArgs: [countId],
      );

      await AuditService.log(
        action: 'inventory_count_completed',
        entityType: 'inventory_count',
        entityId: countId,
        description: 'تم إكمال جلسة الجرد',
        category: 'Inventory',
      );

      LoggingService.info(
        'تم إكمال جلسة الجرد',
        category: 'InventoryCountService',
        data: {'count_id': countId},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إكمال جلسة الجرد',
        category: 'InventoryCountService',
        data: {'count_id': countId, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// اعتماد جلسة الجرد وتطبيق التعديلات
  Future<void> approveInventoryCount(int countId, String approvedBy) async {
    try {
      final count = await getInventoryCountById(countId);
      if (count == null) {
        throw ValidationException('جلسة الجرد غير موجودة');
      }

      if (!count.canApprove) {
        throw ValidationException(
          'لا يمكن اعتماد هذه الجلسة في الحالة الحالية',
        );
      }

      final db = await _databaseHelper.database;

      await db.transaction((txn) async {
        // تطبيق التعديلات على المخزون
        await _applyCountAdjustments(txn, countId);

        // تحديث حالة الجلسة
        final updatedCount = count.copyWith(
          status: InventoryCountStatus.approved,
          approvedBy: approvedBy,
          approvedAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        await txn.update(
          'inventory_counts',
          updatedCount.toMap(),
          where: 'id = ?',
          whereArgs: [countId],
        );
      });

      await AuditService.log(
        action: 'inventory_count_approved',
        entityType: 'inventory_count',
        entityId: countId,
        description: 'تم اعتماد جلسة الجرد وتطبيق التعديلات بواسطة $approvedBy',
        category: 'Inventory',
      );

      LoggingService.info(
        'تم اعتماد جلسة الجرد',
        category: 'InventoryCountService',
        data: {'count_id': countId, 'approved_by': approvedBy},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في اعتماد جلسة الجرد',
        category: 'InventoryCountService',
        data: {'count_id': countId, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// الحصول على جلسة جرد بالمعرف
  Future<InventoryCount?> getInventoryCountById(int id) async {
    try {
      final db = await _databaseHelper.database;

      final result = await db.query(
        'inventory_counts',
        where: 'id = ?',
        whereArgs: [id],
      );

      if (result.isEmpty) return null;

      return InventoryCount.fromMap(result.first);
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على جلسة الجرد',
        category: 'InventoryCountService',
        data: {'count_id': id, 'error': e.toString()},
      );
      return null;
    }
  }

  /// الحصول على جميع جلسات الجرد
  Future<List<InventoryCount>> getAllInventoryCounts({
    InventoryCountStatus? status,
    InventoryCountType? type,
    int? warehouseId,
    DateTime? fromDate,
    DateTime? toDate,
    int? limit,
    int? offset,
  }) async {
    try {
      final db = await _databaseHelper.database;

      String whereClause = '';
      List<dynamic> whereArgs = [];

      if (status != null) {
        whereClause += whereClause.isEmpty ? 'WHERE ' : ' AND ';
        whereClause += 'status = ?';
        whereArgs.add(status.code);
      }

      if (type != null) {
        whereClause += whereClause.isEmpty ? 'WHERE ' : ' AND ';
        whereClause += 'count_type = ?';
        whereArgs.add(type.code);
      }

      if (warehouseId != null) {
        whereClause += whereClause.isEmpty ? 'WHERE ' : ' AND ';
        whereClause += 'warehouse_id = ?';
        whereArgs.add(warehouseId);
      }

      if (fromDate != null) {
        whereClause += whereClause.isEmpty ? 'WHERE ' : ' AND ';
        whereClause += 'scheduled_date >= ?';
        whereArgs.add(fromDate.toIso8601String());
      }

      if (toDate != null) {
        whereClause += whereClause.isEmpty ? 'WHERE ' : ' AND ';
        whereClause += 'scheduled_date <= ?';
        whereArgs.add(toDate.toIso8601String());
      }

      String limitClause = '';
      if (limit != null) {
        limitClause = 'LIMIT $limit';
        if (offset != null) {
          limitClause += ' OFFSET $offset';
        }
      }

      final result = await db.rawQuery('''
        SELECT * FROM inventory_counts
        $whereClause
        ORDER BY scheduled_date DESC, created_at DESC
        $limitClause
      ''', whereArgs);

      return result.map((map) => InventoryCount.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على جلسات الجرد',
        category: 'InventoryCountService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// الحصول على عناصر جلسة الجرد
  Future<List<InventoryCountItem>> getCountItems(int countId) async {
    try {
      final db = await _databaseHelper.database;

      final result = await db.rawQuery(
        '''
        SELECT ici.*, i.name as item_name, i.code as item_code, i.unit,
               wl.name as location_name, w.name as warehouse_name
        FROM inventory_count_items ici
        JOIN items i ON ici.item_id = i.id
        JOIN warehouse_locations wl ON ici.location_id = wl.id
        JOIN warehouses w ON wl.warehouse_id = w.id
        WHERE ici.count_id = ?
        ORDER BY i.name ASC, wl.name ASC
      ''',
        [countId],
      );

      return result.map((map) => InventoryCountItem.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على عناصر الجرد',
        category: 'InventoryCountService',
        data: {'count_id': countId, 'error': e.toString()},
      );
      return [];
    }
  }

  /// الحصول على إحصائيات جلسة الجرد
  Future<Map<String, dynamic>> getCountStatistics(int countId) async {
    try {
      final db = await _databaseHelper.database;

      final result = await db.rawQuery(
        '''
        SELECT
          COUNT(*) as total_items,
          COUNT(CASE WHEN counted_quantity IS NOT NULL THEN 1 END) as counted_items,
          COUNT(CASE WHEN variance > 0 THEN 1 END) as positive_variances,
          COUNT(CASE WHEN variance < 0 THEN 1 END) as negative_variances,
          COUNT(CASE WHEN variance = 0 THEN 1 END) as no_variances,
          SUM(CASE WHEN variance > 0 THEN variance ELSE 0 END) as total_positive_variance,
          SUM(CASE WHEN variance < 0 THEN ABS(variance) ELSE 0 END) as total_negative_variance,
          AVG(ABS(variance)) as average_variance
        FROM inventory_count_items
        WHERE count_id = ?
      ''',
        [countId],
      );

      if (result.isNotEmpty) {
        final row = result.first;
        final totalItems = (row['total_items'] as int?) ?? 0;
        final countedItems = (row['counted_items'] as int?) ?? 0;

        return {
          'total_items': totalItems,
          'counted_items': countedItems,
          'remaining_items': totalItems - countedItems,
          'completion_percentage': totalItems > 0
              ? (countedItems / totalItems) * 100
              : 0.0,
          'positive_variances': row['positive_variances'] ?? 0,
          'negative_variances': row['negative_variances'] ?? 0,
          'no_variances': row['no_variances'] ?? 0,
          'total_positive_variance':
              (row['total_positive_variance'] as num?)?.toDouble() ?? 0.0,
          'total_negative_variance':
              (row['total_negative_variance'] as num?)?.toDouble() ?? 0.0,
          'average_variance':
              (row['average_variance'] as num?)?.toDouble() ?? 0.0,
        };
      }

      return {};
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على إحصائيات الجرد',
        category: 'InventoryCountService',
        data: {'count_id': countId, 'error': e.toString()},
      );
      return {};
    }
  }

  /// إنشاء عناصر الجرد من المخزون الحالي
  Future<void> _generateCountItems(dynamic txn, InventoryCount count) async {
    try {
      String whereClause = 'ils.quantity > 0';
      List<dynamic> whereArgs = [];

      if (count.warehouseId != null) {
        whereClause += ' AND ils.warehouse_id = ?';
        whereArgs.add(count.warehouseId);
      }

      if (count.locationId != null) {
        whereClause += ' AND ils.location_id = ?';
        whereArgs.add(count.locationId);
      }

      final stockItems = await txn.rawQuery('''
        SELECT ils.item_id, ils.location_id, ils.quantity
        FROM item_location_stock ils
        WHERE $whereClause
      ''', whereArgs);

      for (final stockItem in stockItems) {
        final countItem = InventoryCountItem(
          countId: count.id!,
          itemId: stockItem['item_id'] as int,
          locationId: stockItem['location_id'] as int,
          systemQuantity: (stockItem['quantity'] as num).toDouble(),
        );

        await txn.insert('inventory_count_items', countItem.toMap());
      }

      LoggingService.info(
        'تم إنشاء عناصر الجرد',
        category: 'InventoryCountService',
        data: {'count_id': count.id, 'items_count': stockItems.length},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء عناصر الجرد',
        category: 'InventoryCountService',
        data: {'count_id': count.id, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// تطبيق تعديلات الجرد على المخزون
  Future<void> _applyCountAdjustments(dynamic txn, int countId) async {
    try {
      // الحصول على معلومات جلسة الجرد
      final countResult = await txn.query(
        'inventory_counts',
        where: 'id = ?',
        whereArgs: [countId],
      );

      if (countResult.isEmpty) {
        throw ValidationException('جلسة الجرد غير موجودة');
      }

      final count = InventoryCount.fromMap(countResult.first);
      final warehouseId =
          count.warehouseId ?? 1; // استخدام المخزن الافتراضي إذا لم يكن محدد

      // الحصول على العناصر التي لها فروقات
      final varianceItems = await txn.rawQuery(
        '''
        SELECT * FROM inventory_count_items
        WHERE count_id = ? AND variance != 0 AND variance IS NOT NULL
      ''',
        [countId],
      );

      for (final item in varianceItems) {
        final countItem = InventoryCountItem.fromMap(item);

        if (countItem.variance != null && countItem.variance != 0) {
          // الحصول على التكلفة الحالية للصنف في الموقع
          final currentUnitCost = await _getCurrentUnitCost(
            countItem.itemId,
            countItem.locationId,
          );

          // تحديث رصيد المخزون
          await _stockService.updateItemLocationStock(
            itemId: countItem.itemId,
            warehouseId: warehouseId,
            locationId: countItem.locationId,
            quantity: countItem.countedQuantity!,
            unitCost: currentUnitCost,
            movementType: 'adjustment',
            description: 'تعديل من جلسة الجرد رقم $countId',
          );

          // تسجيل حركة المخزون
          final movementUnitCost = await _getCurrentUnitCost(
            countItem.itemId,
            countItem.locationId,
          );
          final totalCost = countItem.variance!.abs() * movementUnitCost;

          final movement = InventoryMovement(
            itemId: countItem.itemId,
            locationId: countItem.locationId,
            movementType: 'adjustment',
            quantity: countItem.variance!.abs(),
            unitCost: movementUnitCost,
            totalCost: totalCost,
            referenceType: 'inventory_count',
            referenceId: countId,
            description:
                'تعديل من جلسة الجرد - ${countItem.variance! > 0 ? 'زيادة' : 'نقص'}: ${countItem.variance!.abs()}',
            movementDate: DateTime.now(),
          );

          await txn.insert('inventory_movements', movement.toMap());
        }
      }

      LoggingService.info(
        'تم تطبيق تعديلات الجرد',
        category: 'InventoryCountService',
        data: {'count_id': countId, 'adjustments_count': varianceItems.length},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في تطبيق تعديلات الجرد',
        category: 'InventoryCountService',
        data: {'count_id': countId, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// التحقق من صحة بيانات جلسة الجرد
  Future<void> _validateInventoryCount(InventoryCount count) async {
    if (count.countNumber.trim().isEmpty) {
      throw ValidationException('رقم الجرد مطلوب');
    }

    if (count.title.trim().isEmpty) {
      throw ValidationException('عنوان الجرد مطلوب');
    }

    if (count.scheduledDate.isBefore(
      DateTime.now().subtract(const Duration(days: 1)),
    )) {
      throw ValidationException('تاريخ الجرد لا يمكن أن يكون في الماضي');
    }

    // التحقق من عدم تكرار رقم الجرد
    final db = await _databaseHelper.database;
    final existing = await db.query(
      'inventory_counts',
      where: 'count_number = ? AND id != ?',
      whereArgs: [count.countNumber, count.id ?? 0],
    );

    if (existing.isNotEmpty) {
      throw ValidationException('رقم الجرد موجود مسبقاً');
    }
  }

  /// توليد رقم جرد تلقائي
  Future<String> generateCountNumber() async {
    try {
      final now = DateTime.now();
      final prefix = 'INV${now.year}${now.month.toString().padLeft(2, '0')}';

      final db = await _databaseHelper.database;
      final result = await db.rawQuery(
        '''
        SELECT COUNT(*) as count
        FROM inventory_counts
        WHERE count_number LIKE ?
      ''',
        ['$prefix%'],
      );

      final count = (result.first['count'] as int?) ?? 0;
      final sequence = (count + 1).toString().padLeft(4, '0');

      return '$prefix$sequence';
    } catch (e) {
      LoggingService.error(
        'خطأ في توليد رقم الجرد',
        category: 'InventoryCountService',
        data: {'error': e.toString()},
      );
      return 'INV${DateTime.now().millisecondsSinceEpoch}';
    }
  }

  /// الحصول على التكلفة الحالية للصنف في الموقع
  Future<double> _getCurrentUnitCost(int itemId, int locationId) async {
    try {
      // محاولة الحصول على التكلفة من رصيد الموقع
      final locationStock = await _stockService.getItemLocationStock(
        itemId,
        locationId,
      );
      if (locationStock != null && locationStock.averageCost > 0) {
        return locationStock.averageCost;
      }

      // إذا لم توجد تكلفة في الموقع، نحصل على التكلفة من الصنف نفسه
      final db = await _databaseHelper.database;
      final result = await db.query(
        'items',
        columns: ['cost_price'],
        where: 'id = ?',
        whereArgs: [itemId],
      );

      if (result.isNotEmpty) {
        final costPrice =
            (result.first['cost_price'] as num?)?.toDouble() ?? 0.0;
        return costPrice;
      }

      // إذا لم توجد أي تكلفة، نعيد صفر
      return 0.0;
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على تكلفة الصنف',
        category: 'InventoryCountService',
        data: {
          'item_id': itemId,
          'location_id': locationId,
          'error': e.toString(),
        },
      );
      return 0.0;
    }
  }
}
