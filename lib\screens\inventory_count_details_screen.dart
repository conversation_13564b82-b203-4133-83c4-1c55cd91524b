/// شاشة تفاصيل جلسة الجرد
/// تعرض تفاصيل جلسة الجرد مع إمكانية إدارة العناصر والإحصائيات
library;

import 'package:flutter/material.dart';
import '../models/inventory_count.dart';
import '../services/inventory_count_service.dart';
import '../constants/app_colors.dart';
import '../widgets/loading_widget.dart';

class InventoryCountDetailsScreen extends StatefulWidget {
  final InventoryCount count;

  const InventoryCountDetailsScreen({super.key, required this.count});

  @override
  State<InventoryCountDetailsScreen> createState() =>
      _InventoryCountDetailsScreenState();
}

class _InventoryCountDetailsScreenState
    extends State<InventoryCountDetailsScreen>
    with TickerProviderStateMixin {
  final InventoryCountService _countService = InventoryCountService();

  bool _isLoading = true;
  bool _isProcessing = false;

  late InventoryCount _count;
  List<InventoryCountItem> _countItems = [];
  Map<String, dynamic> _statistics = {};

  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _count = widget.count;
    _tabController = TabController(length: 3, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);
    try {
      final items = await _countService.getCountItems(_count.id!);
      final stats = await _countService.getCountStatistics(_count.id!);

      setState(() {
        _countItems = items;
        _statistics = stats;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      _showErrorSnackBar('خطأ في تحميل البيانات: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_count.title),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'التفاصيل', icon: Icon(Icons.info_outline)),
            Tab(text: 'العناصر', icon: Icon(Icons.list)),
            Tab(text: 'الإحصائيات', icon: Icon(Icons.analytics)),
          ],
        ),
        actions: [
          if (_count.canStart)
            IconButton(
              icon: _isProcessing
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Icon(Icons.play_arrow),
              onPressed: _isProcessing ? null : _startCount,
              tooltip: 'بدء الجرد',
            ),
          if (_count.canComplete)
            IconButton(
              icon: _isProcessing
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Icon(Icons.check_circle),
              onPressed: _isProcessing ? null : _completeCount,
              tooltip: 'إكمال الجرد',
            ),
          if (_count.canApprove)
            IconButton(
              icon: _isProcessing
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Icon(Icons.approval),
              onPressed: _isProcessing ? null : _approveCount,
              tooltip: 'اعتماد الجرد',
            ),
        ],
      ),
      body: _isLoading
          ? const LoadingWidget()
          : TabBarView(
              controller: _tabController,
              children: [
                _buildDetailsTab(),
                _buildItemsTab(),
                _buildStatisticsTab(),
              ],
            ),
    );
  }

  Widget _buildDetailsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildInfoCard(),
          const SizedBox(height: 16),
          _buildStatusCard(),
          const SizedBox(height: 16),
          _buildTimingCard(),
        ],
      ),
    );
  }

  Widget _buildInfoCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معلومات الجرد',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildInfoRow('رقم الجرد', _count.countNumber),
            _buildInfoRow('العنوان', _count.title),
            if (_count.description?.isNotEmpty == true)
              _buildInfoRow('الوصف', _count.description!),
            _buildInfoRow('نوع الجرد', _count.typeDisplay),
            _buildInfoRow('تاريخ الجدولة', _formatDate(_count.scheduledDate)),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'حالة الجرد',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildStatusChip(_count.status),
                const Spacer(),
                if (_statistics.isNotEmpty)
                  Text(
                    'مكتمل: ${(_statistics['completion_percentage'] ?? 0).toStringAsFixed(1)}%',
                    style: const TextStyle(fontWeight: FontWeight.w500),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTimingCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'التوقيتات',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildInfoRow('تاريخ الإنشاء', _formatDateTime(_count.createdAt)),
            if (_count.startedAt != null)
              _buildInfoRow('تاريخ البدء', _formatDateTime(_count.startedAt!)),
            if (_count.completedAt != null)
              _buildInfoRow(
                'تاريخ الإكمال',
                _formatDateTime(_count.completedAt!),
              ),
            if (_count.approvedAt != null)
              _buildInfoRow(
                'تاريخ الاعتماد',
                _formatDateTime(_count.approvedAt!),
              ),
            if (_count.countedBy?.isNotEmpty == true)
              _buildInfoRow('تم العد بواسطة', _count.countedBy!),
            if (_count.approvedBy?.isNotEmpty == true)
              _buildInfoRow('تم الاعتماد بواسطة', _count.approvedBy!),
          ],
        ),
      ),
    );
  }

  Widget _buildItemsTab() {
    if (_countItems.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.inventory_2_outlined, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'لا توجد عناصر للجرد',
              style: TextStyle(fontSize: 16, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _countItems.length,
      itemBuilder: (context, index) => _buildItemCard(_countItems[index]),
    );
  }

  Widget _buildStatisticsTab() {
    if (_statistics.isEmpty) {
      return const Center(child: Text('لا توجد إحصائيات متاحة'));
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildStatCard(
            'إجمالي العناصر',
            _statistics['total_items']?.toString() ?? '0',
          ),
          _buildStatCard(
            'العناصر المعدودة',
            _statistics['counted_items']?.toString() ?? '0',
          ),
          _buildStatCard(
            'العناصر المتبقية',
            _statistics['remaining_items']?.toString() ?? '0',
          ),
          _buildStatCard(
            'نسبة الإكمال',
            '${(_statistics['completion_percentage'] ?? 0).toStringAsFixed(1)}%',
          ),
          _buildStatCard(
            'فروقات موجبة',
            _statistics['positive_variances']?.toString() ?? '0',
          ),
          _buildStatCard(
            'فروقات سالبة',
            _statistics['negative_variances']?.toString() ?? '0',
          ),
          _buildStatCard(
            'بدون فروقات',
            _statistics['no_variances']?.toString() ?? '0',
          ),
        ],
      ),
    );
  }

  Widget _buildItemCard(InventoryCountItem item) {
    final hasVariance = item.variance != null && item.variance != 0;
    final varianceColor = hasVariance
        ? (item.variance! > 0 ? Colors.green : Colors.red)
        : Colors.grey;

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        title: Text(item.itemName ?? 'صنف غير معروف'),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('الكود: ${item.itemCode ?? 'غير محدد'}'),
            Text('الموقع: ${item.locationName ?? 'غير محدد'}'),
            Row(
              children: [
                Text('النظام: ${item.systemQuantity}'),
                const SizedBox(width: 16),
                Text(
                  'المعدود: ${item.countedQuantity?.toString() ?? 'لم يتم العد'}',
                ),
                if (hasVariance) ...[
                  const SizedBox(width: 16),
                  Text(
                    'الفرق: ${item.variance!.toStringAsFixed(2)}',
                    style: TextStyle(
                      color: varianceColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
        trailing: item.countedQuantity != null
            ? Icon(Icons.check_circle, color: Colors.green)
            : Icon(Icons.pending, color: Colors.orange),
      ),
    );
  }

  Widget _buildStatCard(String title, String value) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        title: Text(title),
        trailing: Text(
          value,
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  Widget _buildStatusChip(InventoryCountStatus status) {
    Color backgroundColor;
    Color textColor;
    IconData icon;

    switch (status) {
      case InventoryCountStatus.draft:
        backgroundColor = Colors.grey.shade100;
        textColor = Colors.grey.shade700;
        icon = Icons.edit;
        break;
      case InventoryCountStatus.inProgress:
        backgroundColor = Colors.blue.shade100;
        textColor = Colors.blue.shade700;
        icon = Icons.play_arrow;
        break;
      case InventoryCountStatus.completed:
        backgroundColor = Colors.orange.shade100;
        textColor = Colors.orange.shade700;
        icon = Icons.check_circle;
        break;
      case InventoryCountStatus.approved:
        backgroundColor = Colors.green.shade100;
        textColor = Colors.green.shade700;
        icon = Icons.verified;
        break;
      case InventoryCountStatus.cancelled:
        backgroundColor = Colors.red.shade100;
        textColor = Colors.red.shade700;
        icon = Icons.cancel;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: textColor),
          const SizedBox(width: 4),
          Text(
            status.displayName,
            style: TextStyle(
              color: textColor,
              fontWeight: FontWeight.w500,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _formatDateTime(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }

  Future<void> _startCount() async {
    setState(() => _isProcessing = true);
    try {
      await _countService.startInventoryCount(_count.id!, 'المستخدم الحالي');
      _showSuccessSnackBar('تم بدء الجرد بنجاح');
      _loadData();
    } catch (e) {
      _showErrorSnackBar('خطأ في بدء الجرد: $e');
    } finally {
      setState(() => _isProcessing = false);
    }
  }

  Future<void> _completeCount() async {
    setState(() => _isProcessing = true);
    try {
      await _countService.completeInventoryCount(_count.id!);
      _showSuccessSnackBar('تم إكمال الجرد بنجاح');
      _loadData();
    } catch (e) {
      _showErrorSnackBar('خطأ في إكمال الجرد: $e');
    } finally {
      setState(() => _isProcessing = false);
    }
  }

  Future<void> _approveCount() async {
    setState(() => _isProcessing = true);
    try {
      await _countService.approveInventoryCount(_count.id!, 'المستخدم الحالي');
      _showSuccessSnackBar('تم اعتماد الجرد بنجاح');
      _loadData();
    } catch (e) {
      _showErrorSnackBar('خطأ في اعتماد الجرد: $e');
    } finally {
      setState(() => _isProcessing = false);
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.green),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }
}
