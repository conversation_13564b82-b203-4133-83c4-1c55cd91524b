import 'package:flutter/material.dart';
import '../constants/revolutionary_design_colors.dart';
import '../constants/revolutionary_animations.dart';

/// مكونات تفاعلية ثورية ومبتكرة
class RevolutionaryInteractive {

  /// بطاقة تفاعلية مع تأثيرات متقدمة
  static Widget interactiveCard({
    required Widget child,
    required String title,
    String? subtitle,
    IconData? icon,
    VoidCallback? onTap,
    VoidCallback? onLongPress,
    Color? accentColor,
    bool isSelected = false,
    bool hasNotification = false,
  }) {
    return StatefulBuilder(
      builder: (context, setState) {
        return GestureDetector(
          onTap: onTap,
          onLongPress: onLongPress,
          child: AnimatedContainer(
            duration: RevolutionaryAnimations.swift,
            curve: RevolutionaryAnimations.damascusCurve,
            margin: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              gradient: isSelected 
                ? RevolutionaryColors.goldGradient
                : RevolutionaryColors.jasmineGradient,
              border: Border.all(
                color: isSelected 
                  ? RevolutionaryColors.syrianGold
                  : accentColor ?? RevolutionaryColors.damascusSky,
                width: isSelected ? 3 : 2,
              ),
              boxShadow: [
                if (isSelected) ...[
                  RevolutionaryColors.createGlowShadow(
                    color: RevolutionaryColors.syrianGold,
                    blurRadius: 15,
                    spreadRadius: 2,
                  ),
                ] else ...[
                  RevolutionaryColors.createGlowShadow(
                    color: accentColor ?? RevolutionaryColors.damascusSky,
                    blurRadius: 8,
                  ),
                ],
              ],
            ),
            child: Stack(
              children: [
                Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          if (icon != null) ...[
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                gradient: isSelected
                                  ? RevolutionaryColors.damascusGradient
                                  : LinearGradient(
                                      colors: [
                                        (accentColor ?? RevolutionaryColors.damascusSky)
                                            .withValues(alpha: 0.2),
                                        (accentColor ?? RevolutionaryColors.damascusSky)
                                            .withValues(alpha: 0.1),
                                      ],
                                    ),
                                borderRadius: BorderRadius.circular(15),
                              ),
                              child: Icon(
                                icon,
                                color: isSelected 
                                  ? RevolutionaryColors.textOnDark
                                  : accentColor ?? RevolutionaryColors.damascusSky,
                                size: 28,
                              ),
                            ),
                            const SizedBox(width: 16),
                          ],
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  title,
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color: isSelected 
                                      ? RevolutionaryColors.textOnGold
                                      : RevolutionaryColors.textPrimary,
                                    fontFamily: 'Cairo',
                                  ),
                                ),
                                if (subtitle != null) ...[
                                  const SizedBox(height: 4),
                                  Text(
                                    subtitle,
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: isSelected 
                                        ? RevolutionaryColors.textOnGold.withValues(alpha: 0.8)
                                        : RevolutionaryColors.textSecondary,
                                      fontFamily: 'Cairo',
                                    ),
                                  ),
                                ],
                              ],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      child,
                    ],
                  ),
                ),
                
                // مؤشر الإشعار
                if (hasNotification)
                  Positioned(
                    top: 12,
                    right: 12,
                    child: Container(
                      width: 12,
                      height: 12,
                      decoration: const BoxDecoration(
                        color: RevolutionaryColors.errorCoral,
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: RevolutionaryColors.errorCoral,
                            blurRadius: 4,
                            spreadRadius: 1,
                          ),
                        ],
                      ),
                    ),
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// زر عائم مع تأثيرات متقدمة
  static Widget floatingActionButton({
    required VoidCallback onPressed,
    required IconData icon,
    String? tooltip,
    Color? backgroundColor,
    Color? foregroundColor,
    bool isExtended = false,
    String? label,
    bool isPulsing = false,
  }) {
    return StatefulBuilder(
      builder: (context, setState) {
        return AnimatedContainer(
          duration: RevolutionaryAnimations.smooth,
          child: FloatingActionButton.extended(
            onPressed: onPressed,
            icon: Icon(
              icon,
              color: foregroundColor ?? RevolutionaryColors.textOnDark,
            ),
            label: isExtended && label != null 
              ? Text(
                  label,
                  style: TextStyle(
                    color: foregroundColor ?? RevolutionaryColors.textOnDark,
                    fontFamily: 'Cairo',
                    fontWeight: FontWeight.w600,
                  ),
                )
              : const SizedBox.shrink(),
            backgroundColor: backgroundColor ?? RevolutionaryColors.damascusSky,
            elevation: 12,
            tooltip: tooltip,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(25),
            ),
          ),
        );
      },
    );
  }

  /// شريط بحث تفاعلي
  static Widget interactiveSearchBar({
    required Function(String) onSearch,
    String? hint,
    IconData? prefixIcon,
    List<Widget>? actions,
    bool isExpanded = false,
    VoidCallback? onExpand,
  }) {
    return StatefulBuilder(
      builder: (context, setState) {
        return AnimatedContainer(
          duration: RevolutionaryAnimations.smooth,
          curve: RevolutionaryAnimations.damascusCurve,
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
          decoration: BoxDecoration(
            gradient: RevolutionaryColors.jasmineGradient,
            borderRadius: BorderRadius.circular(25),
            border: Border.all(
              color: RevolutionaryColors.damascusSky.withValues(alpha: 0.3),
              width: 2,
            ),
            boxShadow: [
              RevolutionaryColors.createGlowShadow(
                color: RevolutionaryColors.damascusSky,
                blurRadius: 10,
              ),
            ],
          ),
          child: Row(
            children: [
              Icon(
                prefixIcon ?? Icons.search,
                color: RevolutionaryColors.damascusSky,
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: TextField(
                  onChanged: onSearch,
                  style: const TextStyle(
                    fontSize: 16,
                    color: RevolutionaryColors.textPrimary,
                    fontFamily: 'Cairo',
                  ),
                  decoration: InputDecoration(
                    hintText: hint ?? 'البحث...',
                    border: InputBorder.none,
                    hintStyle: const TextStyle(
                      color: RevolutionaryColors.textHint,
                      fontFamily: 'Cairo',
                    ),
                  ),
                ),
              ),
              if (actions != null) ...actions,
            ],
          ),
        );
      },
    );
  }

  /// قائمة تفاعلية مع رسوم متحركة
  static Widget interactiveList({
    required List<Widget> children,
    ScrollController? controller,
    EdgeInsets? padding,
    bool isStaggered = true,
  }) {
    return ListView.builder(
      controller: controller,
      padding: padding ?? const EdgeInsets.all(16),
      itemCount: children.length,
      itemBuilder: (context, index) {
        if (isStaggered) {
          return TweenAnimationBuilder<double>(
            tween: Tween<double>(begin: 0, end: 1),
            duration: Duration(
              milliseconds: 300 + (index * 100),
            ),
            curve: RevolutionaryAnimations.damascusCurve,
            builder: (context, value, child) {
              return Transform.translate(
                offset: Offset(0, 30 * (1 - value)),
                child: Opacity(
                  opacity: value,
                  child: children[index],
                ),
              );
            },
          );
        }
        return children[index];
      },
    );
  }

  /// مؤشر تقدم دائري مخصص
  static Widget revolutionaryProgressIndicator({
    double? value,
    Color? color,
    Color? backgroundColor,
    double strokeWidth = 6.0,
    String? label,
  }) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: 80,
            height: 80,
            child: Stack(
              children: [
                CircularProgressIndicator(
                  value: value,
                  strokeWidth: strokeWidth,
                  color: color ?? RevolutionaryColors.damascusSky,
                  backgroundColor: backgroundColor ?? 
                    RevolutionaryColors.borderLight,
                ),
                if (value != null)
                  Center(
                    child: Text(
                      '${(value * 100).toInt()}%',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: color ?? RevolutionaryColors.damascusSky,
                        fontFamily: 'Cairo',
                      ),
                    ),
                  ),
              ],
            ),
          ),
          if (label != null) ...[
            const SizedBox(height: 12),
            Text(
              label,
              style: const TextStyle(
                fontSize: 14,
                color: RevolutionaryColors.textSecondary,
                fontFamily: 'Cairo',
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// شريط تبديل مخصص
  static Widget revolutionaryToggle({
    required bool value,
    required Function(bool) onChanged,
    String? label,
    IconData? activeIcon,
    IconData? inactiveIcon,
  }) {
    return GestureDetector(
      onTap: () => onChanged(!value),
      child: AnimatedContainer(
        duration: RevolutionaryAnimations.swift,
        curve: RevolutionaryAnimations.damascusCurve,
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
        decoration: BoxDecoration(
          gradient: value 
            ? RevolutionaryColors.damascusGradient
            : RevolutionaryColors.jasmineGradient,
          borderRadius: BorderRadius.circular(25),
          border: Border.all(
            color: value 
              ? RevolutionaryColors.damascusSky
              : RevolutionaryColors.borderMedium,
            width: 2,
          ),
          boxShadow: [
            if (value)
              RevolutionaryColors.createGlowShadow(
                color: RevolutionaryColors.damascusSky,
                blurRadius: 8,
              ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (activeIcon != null || inactiveIcon != null)
              Icon(
                value ? activeIcon : inactiveIcon,
                color: value 
                  ? RevolutionaryColors.textOnDark
                  : RevolutionaryColors.textSecondary,
                size: 20,
              ),
            if (label != null) ...[
              if (activeIcon != null || inactiveIcon != null)
                const SizedBox(width: 8),
              Text(
                label,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: value 
                    ? RevolutionaryColors.textOnDark
                    : RevolutionaryColors.textSecondary,
                  fontFamily: 'Cairo',
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
