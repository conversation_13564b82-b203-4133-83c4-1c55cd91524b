import 'package:flutter/material.dart';
import '../constants/app_colors.dart';
import '../widgets/dashboard_card.dart';
import '../services/enhanced_invoice_service.dart';
import 'interactive_report_screen.dart';
import 'report_performance_screen.dart';

class ReportsScreen extends StatefulWidget {
  const ReportsScreen({super.key});

  @override
  State<ReportsScreen> createState() => _ReportsScreenState();
}

class _ReportsScreenState extends State<ReportsScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  // الخدمات المحسنة
  final EnhancedInvoiceService _enhancedInvoiceService =
      EnhancedInvoiceService();

  // إحصائيات التكامل
  Map<String, dynamic>? _integrationStats;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _animationController.forward();
    _loadIntegrationStats();
  }

  /// تحميل إحصائيات التكامل
  Future<void> _loadIntegrationStats() async {
    try {
      final stats = await _enhancedInvoiceService
          .validateAllInvoicesIntegration();
      if (mounted) {
        setState(() {
          _integrationStats = stats;
        });
      }
    } catch (e) {
      // في حالة الخطأ، لا نعرض رسالة خطأ لأن هذا ليس ضرورياً لعمل الشاشة
      if (mounted) {
        setState(() {
          _integrationStats = null;
        });
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('التقارير المالية'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          // التصدير المتقدم
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _showExportOptions,
            tooltip: 'تصدير التقارير',
          ),
          // الرسوم البيانية المتقدمة
          IconButton(
            icon: const Icon(Icons.bar_chart),
            onPressed: _showVisualizationOptions,
            tooltip: 'الرسوم البيانية المتقدمة',
          ),
          // مراقبة الأداء
          IconButton(
            icon: const Icon(Icons.speed),
            onPressed: () => Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const ReportPerformanceScreen(),
              ),
            ),
            tooltip: 'مراقبة أداء التقارير',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadIntegrationStats,
            tooltip: 'تحديث الإحصائيات',
          ),
        ],
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Column(
          children: [
            // شريط إحصائيات التكامل
            if (_integrationStats != null) _buildIntegrationStatsBar(),

            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: GridView.count(
                  crossAxisCount: 2,
                  crossAxisSpacing: 16,
                  mainAxisSpacing: 16,
                  childAspectRatio: 1.1,
                  children: [
                    DashboardCard(
                      title: 'ميزان المراجعة',
                      subtitle: 'عرض أرصدة جميع الحسابات',
                      icon: Icons.balance,
                      color: AppColors.primary,
                      onTap: () =>
                          _navigateToReport('trial_balance', 'ميزان المراجعة'),
                    ),
                    DashboardCard(
                      title: 'قائمة الدخل',
                      subtitle: 'الإيرادات والمصروفات',
                      icon: Icons.trending_up,
                      color: AppColors.success,
                      onTap: () =>
                          _navigateToReport('profit_loss', 'قائمة الدخل'),
                    ),
                    DashboardCard(
                      title: 'الميزانية العمومية',
                      subtitle: 'الأصول والخصوم',
                      icon: Icons.account_balance,
                      color: AppColors.info,
                      onTap: () => _navigateToReport(
                        'balance_sheet',
                        'الميزانية العمومية',
                      ),
                    ),
                    DashboardCard(
                      title: 'كشف حساب',
                      subtitle: 'تفاصيل حساب معين',
                      icon: Icons.description,
                      color: AppColors.secondary,
                      onTap: () =>
                          _navigateToReport('account_movements', 'كشف حساب'),
                    ),
                    DashboardCard(
                      title: 'تقرير العملاء',
                      subtitle: 'أرصدة وحركة العملاء',
                      icon: Icons.people,
                      color: AppColors.warning,
                      onTap: () =>
                          _navigateToReport('customer_aging', 'تقرير العملاء'),
                    ),
                    DashboardCard(
                      title: 'تقرير الموردين',
                      subtitle: 'أرصدة وحركة الموردين',
                      icon: Icons.business,
                      color: AppColors.error,
                      onTap: () =>
                          _navigateToReport('supplier_aging', 'تقرير الموردين'),
                    ),
                    DashboardCard(
                      title: 'تقرير المبيعات',
                      subtitle: 'إحصائيات المبيعات',
                      icon: Icons.point_of_sale,
                      color: AppColors.success,
                      onTap: () =>
                          _navigateToReport('sales_analysis', 'تقرير المبيعات'),
                    ),
                    DashboardCard(
                      title: 'تقرير المشتريات',
                      subtitle: 'إحصائيات المشتريات',
                      icon: Icons.shopping_cart,
                      color: AppColors.primary,
                      onTap: () => _navigateToReport(
                        'purchase_analysis',
                        'تقرير المشتريات',
                      ),
                    ),
                    DashboardCard(
                      title: 'اليومية العامة',
                      subtitle: 'جميع القيود المحاسبية',
                      icon: Icons.receipt_long,
                      color: AppColors.secondary,
                      onTap: () => _navigateToReport(
                        'general_journal',
                        'اليومية العامة',
                      ),
                    ),
                    DashboardCard(
                      title: 'الأستاذ العام',
                      subtitle: 'حركة الحسابات',
                      icon: Icons.book,
                      color: AppColors.info,
                      onTap: () =>
                          _navigateToReport('general_ledger', 'الأستاذ العام'),
                    ),
                    DashboardCard(
                      title: 'تقرير الضرائب',
                      subtitle: 'ملخص الضرائب',
                      icon: Icons.receipt,
                      color: AppColors.warning,
                      onTap: () =>
                          _navigateToReport('tax_report', 'تقرير الضرائب'),
                    ),
                    DashboardCard(
                      title: 'التقارير المخصصة',
                      subtitle: 'تقارير حسب الطلب',
                      icon: Icons.analytics,
                      color: AppColors.error,
                      onTap: () => _navigateToReport(
                        'custom_reports',
                        'التقارير المخصصة',
                      ),
                    ),

                    // التقارير الجديدة للتكامل
                    DashboardCard(
                      title: 'تقرير التكامل',
                      subtitle: 'حالة التكامل بين الأنظمة',
                      icon: Icons.integration_instructions,
                      color: AppColors.primary,
                      onTap: () => _navigateToReport(
                        'integration_status',
                        'تقرير التكامل',
                      ),
                    ),
                    DashboardCard(
                      title: 'تقرير المخزون المتقدم',
                      subtitle: 'حركات وتحليل المخزون',
                      icon: Icons.inventory_2,
                      color: AppColors.success,
                      onTap: () => _navigateToReport(
                        'advanced_inventory',
                        'تقرير المخزون المتقدم',
                      ),
                    ),
                    DashboardCard(
                      title: 'تحليل الأداء',
                      subtitle: 'إحصائيات أداء النظام',
                      icon: Icons.analytics_outlined,
                      color: AppColors.info,
                      onTap: () => _navigateToReport(
                        'performance_analysis',
                        'تحليل الأداء',
                      ),
                    ),
                    DashboardCard(
                      title: 'سجل المراجعة',
                      subtitle: 'تتبع جميع العمليات',
                      icon: Icons.history,
                      color: AppColors.warning,
                      onTap: () =>
                          _navigateToReport('audit_log', 'سجل المراجعة'),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء شريط إحصائيات التكامل
  Widget _buildIntegrationStatsBar() {
    if (_integrationStats == null) return const SizedBox.shrink();

    final totalInvoices = _integrationStats!['totalInvoices'] ?? 0;
    final integratedInvoices = _integrationStats!['integratedInvoices'] ?? 0;
    final integrationPercentage =
        _integrationStats!['integrationPercentage'] ?? 0.0;
    final issues = _integrationStats!['issues'] as List? ?? [];

    Color statusColor = AppColors.success;
    if (integrationPercentage < 80) {
      statusColor = AppColors.error;
    } else if (integrationPercentage < 95) {
      statusColor = AppColors.warning;
    }

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: statusColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: statusColor.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(
            integrationPercentage >= 95
                ? Icons.check_circle
                : integrationPercentage >= 80
                ? Icons.warning
                : Icons.error,
            color: statusColor,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'حالة التكامل: ${integrationPercentage.toStringAsFixed(1)}%',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: statusColor,
                  ),
                ),
                Text(
                  '$integratedInvoices من $totalInvoices فاتورة متكاملة',
                  style: const TextStyle(fontSize: 12),
                ),
                if (issues.isNotEmpty)
                  Text(
                    '${issues.length} مشكلة تحتاج إلى حل',
                    style: TextStyle(fontSize: 12, color: AppColors.error),
                  ),
              ],
            ),
          ),
          TextButton(
            onPressed: () =>
                _navigateToReport('integration_status', 'تقرير التكامل'),
            child: const Text('عرض التفاصيل'),
          ),
        ],
      ),
    );
  }

  void _navigateToReport(String reportType, String reportTitle) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => InteractiveReportScreen(
          reportType: reportType,
          reportTitle: reportTitle,
        ),
      ),
    );
  }

  void _showExportOptions() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'خيارات التصدير',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.picture_as_pdf, color: Colors.red),
              title: const Text('تصدير PDF'),
              subtitle: const Text('تصدير التقارير بصيغة PDF'),
              onTap: () {
                Navigator.pop(context);
                _exportToPDF();
              },
            ),
            ListTile(
              leading: const Icon(Icons.table_chart, color: Colors.green),
              title: const Text('تصدير Excel'),
              subtitle: const Text('تصدير البيانات بصيغة Excel'),
              onTap: () {
                Navigator.pop(context);
                _exportToExcel();
              },
            ),
            ListTile(
              leading: const Icon(Icons.code, color: Colors.blue),
              title: const Text('تصدير JSON'),
              subtitle: const Text('تصدير البيانات الخام'),
              onTap: () {
                Navigator.pop(context);
                _exportToJSON();
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showVisualizationOptions() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'الرسوم البيانية المتقدمة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.pie_chart, color: Colors.orange),
              title: const Text('الرسوم الدائرية'),
              subtitle: const Text('عرض البيانات بالرسوم الدائرية'),
              onTap: () {
                Navigator.pop(context);
                _showPieCharts();
              },
            ),
            ListTile(
              leading: const Icon(Icons.show_chart, color: Colors.purple),
              title: const Text('الرسوم الخطية'),
              subtitle: const Text('تتبع الاتجاهات عبر الزمن'),
              onTap: () {
                Navigator.pop(context);
                _showLineCharts();
              },
            ),
            ListTile(
              leading: const Icon(Icons.scatter_plot, color: Colors.teal),
              title: const Text('الرسوم التفاعلية'),
              subtitle: const Text('رسوم بيانية تفاعلية متقدمة'),
              onTap: () {
                Navigator.pop(context);
                _showInteractiveCharts();
              },
            ),
          ],
        ),
      ),
    );
  }

  void _exportToPDF() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('جاري تصدير التقرير إلى PDF...'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _exportToExcel() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('جاري تصدير البيانات إلى Excel...'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _exportToJSON() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('جاري تصدير البيانات إلى JSON...'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _showPieCharts() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('عرض الرسوم الدائرية - قريباً'),
        backgroundColor: Colors.purple,
      ),
    );
  }

  void _showLineCharts() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('عرض الرسوم الخطية - قريباً'),
        backgroundColor: Colors.indigo,
      ),
    );
  }

  void _showInteractiveCharts() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('عرض الرسوم التفاعلية - قريباً'),
        backgroundColor: Colors.teal,
      ),
    );
  }
}
