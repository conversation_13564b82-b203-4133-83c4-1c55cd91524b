import 'package:flutter/material.dart';

/// نظام ألوان ثوري ومبتكر لـ Smart Ledger
/// مستوحى من الطبيعة السورية والتراث العربي مع لمسة عصرية
class RevolutionaryColors {
  // === الألوان الأساسية - مستوحاة من سماء دمشق ===
  static const Color damascusSky = Color(0xFF2E86AB); // أزرق سماء دمشق
  static const Color damascusSkyLight = Color(0xFF5BA3C7); // أزرق فاتح
  static const Color damascusSkyDark = Color(0xFF1B5A7A); // أزرق داكن
  
  // === ألوان الذهب السوري - للعناصر المميزة ===
  static const Color syrianGold = Color(0xFFD4AF37); // ذهبي سوري أصيل
  static const Color syrianGoldLight = Color(0xFFE8C547); // ذهبي فاتح
  static const Color syrianGoldDark = Color(0xFFB8941F); // ذهبي داكن
  
  // === ألوان الياسمين الدمشقي - للنجاح والإيجابية ===
  static const Color jasmineWhite = Color(0xFFFFFDF7); // أبيض الياسمين
  static const Color jasmineGreen = Color(0xFF4A7C59); // أخضر أوراق الياسمين
  static const Color jasminePetal = Color(0xFFF8F6F0); // لون بتلة الياسمين
  
  // === ألوان التراث العربي ===
  static const Color arabicCalligraphy = Color(0xFF2C3E50); // لون الخط العربي
  static const Color manuscriptPaper = Color(0xFFFAF7F2); // لون ورق المخطوطات
  static const Color inkBlue = Color(0xFF34495E); // أزرق الحبر العربي
  
  // === ألوان الطبيعة السورية ===
  static const Color oliveBranch = Color(0xFF8FBC8F); // أخضر غصن الزيتون
  static const Color cedarsGreen = Color(0xFF228B22); // أخضر الأرز
  static const Color desertSand = Color(0xFFF4E4BC); // رمل الصحراء
  static const Color oasisBlue = Color(0xFF4682B4); // أزرق الواحة
  
  // === ألوان الحالة المبتكرة ===
  static const Color successGlow = Color(0xFF00E676); // نجاح متوهج
  static const Color warningAmber = Color(0xFFFFB300); // تحذير كهرماني
  static const Color errorCoral = Color(0xFFFF5722); // خطأ مرجاني
  static const Color infoTurquoise = Color(0xFF00BCD4); // معلومات فيروزية
  
  // === تدرجات ثورية ===
  static const LinearGradient damascusGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [damascusSkyLight, damascusSky, damascusSkyDark],
    stops: [0.0, 0.5, 1.0],
  );
  
  static const LinearGradient goldGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [syrianGoldLight, syrianGold, syrianGoldDark],
    stops: [0.0, 0.6, 1.0],
  );
  
  static const LinearGradient jasmineGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [jasmineWhite, jasminePetal, Color(0xFFF0EDE5)],
    stops: [0.0, 0.7, 1.0],
  );
  
  static const LinearGradient heritageGradient = LinearGradient(
    begin: Alignment.topRight,
    end: Alignment.bottomLeft,
    colors: [manuscriptPaper, Color(0xFFF5F2E8), desertSand],
    stops: [0.0, 0.5, 1.0],
  );
  
  // === تدرجات متحركة للعناصر التفاعلية ===
  static const LinearGradient interactiveGradient = LinearGradient(
    begin: Alignment.centerLeft,
    end: Alignment.centerRight,
    colors: [damascusSky, syrianGold, damascusSky],
    stops: [0.0, 0.5, 1.0],
  );
  
  static const LinearGradient hoverGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [syrianGoldLight, damascusSkyLight],
    stops: [0.0, 1.0],
  );
  
  // === ألوان الظلال المتقدمة ===
  static const Color shadowSoft = Color(0x10000000); // ظل ناعم
  static const Color shadowMedium = Color(0x20000000); // ظل متوسط
  static const Color shadowStrong = Color(0x30000000); // ظل قوي
  static const Color shadowGold = Color(0x20D4AF37); // ظل ذهبي
  static const Color shadowBlue = Color(0x202E86AB); // ظل أزرق
  
  // === ألوان النص المتطورة ===
  static const Color textPrimary = arabicCalligraphy;
  static const Color textSecondary = Color(0xFF5D6D7E);
  static const Color textHint = Color(0xFF95A5A6);
  static const Color textOnDark = jasmineWhite;
  static const Color textOnGold = arabicCalligraphy;
  static const Color textAccent = damascusSky;
  
  // === ألوان الخلفيات المتدرجة ===
  static const Color backgroundPrimary = jasmineWhite;
  static const Color backgroundSecondary = jasminePetal;
  static const Color backgroundCard = Color(0xFFFFFFFF);
  static const Color backgroundOverlay = Color(0x80000000);
  
  // === ألوان الحدود الأنيقة ===
  static const Color borderLight = Color(0xFFE8E8E8);
  static const Color borderMedium = Color(0xFFD0D0D0);
  static const Color borderAccent = damascusSky;
  static const Color borderGold = syrianGold;
  
  // === دوال مساعدة للألوان المتقدمة ===
  static Color withGlow(Color color, double intensity) {
    return color.withValues(alpha: intensity);
  }
  
  static LinearGradient createCustomGradient({
    required Color startColor,
    required Color endColor,
    Alignment begin = Alignment.topLeft,
    Alignment end = Alignment.bottomRight,
  }) {
    return LinearGradient(
      begin: begin,
      end: end,
      colors: [startColor, endColor],
    );
  }
  
  static BoxShadow createGlowShadow({
    required Color color,
    double blurRadius = 8.0,
    double spreadRadius = 0.0,
    Offset offset = const Offset(0, 2),
  }) {
    return BoxShadow(
      color: color.withValues(alpha: 0.3),
      blurRadius: blurRadius,
      spreadRadius: spreadRadius,
      offset: offset,
    );
  }
  
  // === ألوان خاصة للمحاسبة ===
  static const Color profitGreen = Color(0xFF27AE60); // ربح
  static const Color lossRed = Color(0xFFE74C3C); // خسارة
  static const Color balanceBlue = damascusSky; // رصيد
  static const Color debitOrange = Color(0xFFE67E22); // مدين
  static const Color creditPurple = Color(0xFF9B59B6); // دائن
  
  // === ألوان الرسوم البيانية ===
  static const List<Color> chartColors = [
    damascusSky,
    syrianGold,
    oliveBranch,
    errorCoral,
    infoTurquoise,
    warningAmber,
    cedarsGreen,
    Color(0xFF8E44AD),
    Color(0xFFE74C3C),
    Color(0xFF3498DB),
  ];
  
  // === ألوان الحالات المتقدمة ===
  static const Color pendingYellow = Color(0xFFF39C12);
  static const Color approvedGreen = successGlow;
  static const Color rejectedRed = errorCoral;
  static const Color draftGray = Color(0xFF7F8C8D);
  static const Color archivedBrown = Color(0xFF8D6E63);
}
