/// نموذج جلسة الجرد
/// يمثل جلسة جرد كاملة للمخزون مع تتبع الحالة والنتائج
library;

import 'dart:convert';

/// حالات جلسة الجرد
enum InventoryCountStatus {
  draft('draft', 'مسودة'),
  inProgress('in_progress', 'قيد التنفيذ'),
  completed('completed', 'مكتملة'),
  cancelled('cancelled', 'ملغاة'),
  approved('approved', 'معتمدة');

  const InventoryCountStatus(this.code, this.displayName);
  final String code;
  final String displayName;

  static InventoryCountStatus fromCode(String code) {
    return InventoryCountStatus.values.firstWhere(
      (status) => status.code == code,
      orElse: () => InventoryCountStatus.draft,
    );
  }
}

/// أنواع الجرد
enum InventoryCountType {
  full('full', 'جرد شامل'),
  partial('partial', 'جرد جزئي'),
  cycle('cycle', 'جرد دوري'),
  spot('spot', 'جرد فوري');

  const InventoryCountType(this.code, this.displayName);
  final String code;
  final String displayName;

  static InventoryCountType fromCode(String code) {
    return InventoryCountType.values.firstWhere(
      (type) => type.code == code,
      orElse: () => InventoryCountType.partial,
    );
  }
}

/// نموذج جلسة الجرد
class InventoryCount {
  final int? id;
  final String countNumber;
  final String title;
  final String? description;
  final InventoryCountType countType;
  final InventoryCountStatus status;
  final int? warehouseId;
  final int? locationId;
  final DateTime scheduledDate;
  final DateTime? startedAt;
  final DateTime? completedAt;
  final String? countedBy;
  final String? approvedBy;
  final DateTime? approvedAt;
  final Map<String, dynamic> settings;
  final DateTime createdAt;
  final DateTime updatedAt;

  InventoryCount({
    this.id,
    required this.countNumber,
    required this.title,
    this.description,
    this.countType = InventoryCountType.partial,
    this.status = InventoryCountStatus.draft,
    this.warehouseId,
    this.locationId,
    required this.scheduledDate,
    this.startedAt,
    this.completedAt,
    this.countedBy,
    this.approvedBy,
    this.approvedAt,
    this.settings = const {},
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'count_number': countNumber,
      'title': title,
      'description': description,
      'count_type': countType.code,
      'status': status.code,
      'warehouse_id': warehouseId,
      'location_id': locationId,
      'scheduled_date': scheduledDate.toIso8601String(),
      'started_at': startedAt?.toIso8601String(),
      'completed_at': completedAt?.toIso8601String(),
      'counted_by': countedBy,
      'approved_by': approvedBy,
      'approved_at': approvedAt?.toIso8601String(),
      'settings': settings.isNotEmpty ? settings.toString() : null,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  factory InventoryCount.fromMap(Map<String, dynamic> map) {
    return InventoryCount(
      id: map['id']?.toInt(),
      countNumber: map['count_number'] ?? '',
      title: map['title'] ?? '',
      description: map['description'],
      countType: InventoryCountType.fromCode(map['count_type'] ?? 'partial'),
      status: InventoryCountStatus.fromCode(map['status'] ?? 'draft'),
      warehouseId: map['warehouse_id']?.toInt(),
      locationId: map['location_id']?.toInt(),
      scheduledDate: DateTime.parse(
        map['scheduled_date'] ?? DateTime.now().toIso8601String(),
      ),
      startedAt: map['started_at'] != null
          ? DateTime.parse(map['started_at'])
          : null,
      completedAt: map['completed_at'] != null
          ? DateTime.parse(map['completed_at'])
          : null,
      countedBy: map['counted_by'],
      approvedBy: map['approved_by'],
      approvedAt: map['approved_at'] != null
          ? DateTime.parse(map['approved_at'])
          : null,
      settings: _parseJsonData(map['settings']),
      createdAt: DateTime.parse(
        map['created_at'] ?? DateTime.now().toIso8601String(),
      ),
      updatedAt: DateTime.parse(
        map['updated_at'] ?? DateTime.now().toIso8601String(),
      ),
    );
  }

  /// تحليل بيانات JSON
  static Map<String, dynamic> _parseJsonData(dynamic data) {
    if (data == null) return {};
    if (data is Map<String, dynamic>) return data;
    if (data is String) {
      try {
        final decoded = jsonDecode(data);
        return decoded is Map<String, dynamic> ? decoded : {};
      } catch (e) {
        return {};
      }
    }
    return {};
  }

  InventoryCount copyWith({
    int? id,
    String? countNumber,
    String? title,
    String? description,
    InventoryCountType? countType,
    InventoryCountStatus? status,
    int? warehouseId,
    int? locationId,
    DateTime? scheduledDate,
    DateTime? startedAt,
    DateTime? completedAt,
    String? countedBy,
    String? approvedBy,
    DateTime? approvedAt,
    Map<String, dynamic>? settings,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return InventoryCount(
      id: id ?? this.id,
      countNumber: countNumber ?? this.countNumber,
      title: title ?? this.title,
      description: description ?? this.description,
      countType: countType ?? this.countType,
      status: status ?? this.status,
      warehouseId: warehouseId ?? this.warehouseId,
      locationId: locationId ?? this.locationId,
      scheduledDate: scheduledDate ?? this.scheduledDate,
      startedAt: startedAt ?? this.startedAt,
      completedAt: completedAt ?? this.completedAt,
      countedBy: countedBy ?? this.countedBy,
      approvedBy: approvedBy ?? this.approvedBy,
      approvedAt: approvedAt ?? this.approvedAt,
      settings: settings ?? this.settings,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'InventoryCount(id: $id, countNumber: $countNumber, title: $title, status: ${status.displayName})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is InventoryCount && other.id == id;
  }

  @override
  int get hashCode {
    return id.hashCode;
  }

  // خصائص مساعدة
  bool get isDraft => status == InventoryCountStatus.draft;
  bool get isInProgress => status == InventoryCountStatus.inProgress;
  bool get isCompleted => status == InventoryCountStatus.completed;
  bool get isCancelled => status == InventoryCountStatus.cancelled;
  bool get isApproved => status == InventoryCountStatus.approved;

  bool get canStart => status == InventoryCountStatus.draft;
  bool get canComplete => status == InventoryCountStatus.inProgress;
  bool get canApprove => status == InventoryCountStatus.completed;
  bool get canCancel =>
      status == InventoryCountStatus.draft ||
      status == InventoryCountStatus.inProgress;

  Duration? get duration {
    if (startedAt != null && completedAt != null) {
      return completedAt!.difference(startedAt!);
    }
    return null;
  }

  bool get isOverdue {
    return status == InventoryCountStatus.draft &&
        DateTime.now().isAfter(scheduledDate);
  }

  String get statusDisplay => status.displayName;
  String get typeDisplay => countType.displayName;
}

/// نموذج عنصر الجرد
class InventoryCountItem {
  final int? id;
  final int countId;
  final int itemId;
  final int locationId;
  final double systemQuantity;
  final double? countedQuantity;
  final double? variance;
  final String? notes;
  final bool isRecounted;
  final DateTime? countedAt;
  final String? countedBy;
  final DateTime createdAt;
  final DateTime updatedAt;

  // خصائص إضافية من الاستعلامات
  final String? itemName;
  final String? itemCode;
  final String? locationName;
  final String? warehouseName;

  InventoryCountItem({
    this.id,
    required this.countId,
    required this.itemId,
    required this.locationId,
    required this.systemQuantity,
    this.countedQuantity,
    this.variance,
    this.notes,
    this.isRecounted = false,
    this.countedAt,
    this.countedBy,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.itemName,
    this.itemCode,
    this.locationName,
    this.warehouseName,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'count_id': countId,
      'item_id': itemId,
      'location_id': locationId,
      'system_quantity': systemQuantity,
      'counted_quantity': countedQuantity,
      'variance': variance,
      'notes': notes,
      'is_recounted': isRecounted ? 1 : 0,
      'counted_at': countedAt?.toIso8601String(),
      'counted_by': countedBy,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  factory InventoryCountItem.fromMap(Map<String, dynamic> map) {
    return InventoryCountItem(
      id: map['id']?.toInt(),
      countId: map['count_id']?.toInt() ?? 0,
      itemId: map['item_id']?.toInt() ?? 0,
      locationId: map['location_id']?.toInt() ?? 0,
      systemQuantity: map['system_quantity']?.toDouble() ?? 0.0,
      countedQuantity: map['counted_quantity']?.toDouble(),
      variance: map['variance']?.toDouble(),
      notes: map['notes'],
      isRecounted: (map['is_recounted'] ?? 0) == 1,
      countedAt: map['counted_at'] != null
          ? DateTime.parse(map['counted_at'])
          : null,
      countedBy: map['counted_by'],
      createdAt: DateTime.parse(
        map['created_at'] ?? DateTime.now().toIso8601String(),
      ),
      updatedAt: DateTime.parse(
        map['updated_at'] ?? DateTime.now().toIso8601String(),
      ),
      itemName: map['item_name'],
      itemCode: map['item_code'],
      locationName: map['location_name'],
      warehouseName: map['warehouse_name'],
    );
  }

  InventoryCountItem copyWith({
    int? id,
    int? countId,
    int? itemId,
    int? locationId,
    double? systemQuantity,
    double? countedQuantity,
    double? variance,
    String? notes,
    bool? isRecounted,
    DateTime? countedAt,
    String? countedBy,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? itemName,
    String? itemCode,
    String? locationName,
    String? warehouseName,
  }) {
    return InventoryCountItem(
      id: id ?? this.id,
      countId: countId ?? this.countId,
      itemId: itemId ?? this.itemId,
      locationId: locationId ?? this.locationId,
      systemQuantity: systemQuantity ?? this.systemQuantity,
      countedQuantity: countedQuantity ?? this.countedQuantity,
      variance: variance ?? this.variance,
      notes: notes ?? this.notes,
      isRecounted: isRecounted ?? this.isRecounted,
      countedAt: countedAt ?? this.countedAt,
      countedBy: countedBy ?? this.countedBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      itemName: itemName ?? this.itemName,
      itemCode: itemCode ?? this.itemCode,
      locationName: locationName ?? this.locationName,
      warehouseName: warehouseName ?? this.warehouseName,
    );
  }

  // خصائص مساعدة
  bool get isCounted => countedQuantity != null;
  bool get hasVariance => variance != null && variance != 0;
  bool get isPositiveVariance => variance != null && variance! > 0;
  bool get isNegativeVariance => variance != null && variance! < 0;

  double get variancePercentage {
    if (variance == null || systemQuantity == 0) return 0.0;
    return (variance! / systemQuantity) * 100;
  }

  /// حساب الفرق عند تحديث الكمية المعدودة
  InventoryCountItem updateCountedQuantity(double newCountedQuantity) {
    final newVariance = newCountedQuantity - systemQuantity;
    return copyWith(
      countedQuantity: newCountedQuantity,
      variance: newVariance,
      countedAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }
}
