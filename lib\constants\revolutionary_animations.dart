import 'package:flutter/material.dart';
import 'dart:math' as math;

/// نظام رسوم متحركة ثوري ومبتكر
class RevolutionaryAnimations {
  // === مدة الرسوم المتحركة المتقدمة ===
  static const Duration ultraFast = Duration(milliseconds: 100);
  static const Duration lightning = Duration(milliseconds: 150);
  static const Duration swift = Duration(milliseconds: 250);
  static const Duration smooth = Duration(milliseconds: 400);
  static const Duration elegant = Duration(milliseconds: 600);
  static const Duration majestic = Duration(milliseconds: 800);
  static const Duration epic = Duration(milliseconds: 1200);

  // === منحنيات متقدمة ومخصصة ===
  static const Curve damascusCurve = Curves.fastOutSlowIn;
  static const Curve goldCurve = Curves.elasticOut;
  static const Curve jasmineCurve = Curves.easeInOutCubic;
  static const Curve heritageCurve = Curves.bounceOut;
  static const Curve silkCurve = Curves.easeInOutQuart;

  // منحنيات مخصصة
  static const Curve syrianSpring = Cubic(0.25, 0.46, 0.45, 0.94);
  static const Curve arabicFlow = Cubic(0.17, 0.67, 0.83, 0.67);
  static const Curve calligraphyStroke = Cubic(0.4, 0.0, 0.2, 1.0);

  /// رسم متحرك للبطاقات ثلاثية الأبعاد
  static Widget floating3DCardAnimation({
    required Widget child,
    required Animation<double> animation,
    Duration delay = Duration.zero,
    double maxRotation = 0.1,
    double maxTranslation = 20.0,
  }) {
    final delayedAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: animation,
        curve: Interval(delay.inMilliseconds / 1000.0, 1.0, curve: goldCurve),
      ),
    );

    return AnimatedBuilder(
      animation: delayedAnimation,
      builder: (context, child) {
        final rotationValue = maxRotation * (1 - delayedAnimation.value);
        final translationValue = maxTranslation * (1 - delayedAnimation.value);

        return Transform(
          alignment: Alignment.center,
          transform: Matrix4.identity()
            ..setEntry(3, 2, 0.001) // منظور ثلاثي الأبعاد
            ..rotateX(rotationValue)
            ..rotateY(rotationValue * 0.5)
            ..translate(0.0, translationValue, 0.0),
          child: Opacity(opacity: delayedAnimation.value, child: child),
        );
      },
      child: child,
    );
  }

  /// رسم متحرك للنص مع تأثير الكتابة
  static Widget typewriterAnimation({
    required String text,
    required Animation<double> animation,
    TextStyle? style,
    Duration characterDelay = const Duration(milliseconds: 50),
  }) {
    return AnimatedBuilder(
      animation: animation,
      builder: (context, child) {
        final charactersToShow = (text.length * animation.value).round();
        final visibleText = text.substring(0, charactersToShow);

        return Text(
          visibleText + (animation.value < 1.0 ? '|' : ''),
          style: style,
        );
      },
    );
  }

  /// رسم متحرك للأرقام مع تأثير العد
  static Widget countingAnimation({
    required double begin,
    required double end,
    required Animation<double> animation,
    TextStyle? style,
    String? prefix,
    String? suffix,
    int decimalPlaces = 0,
  }) {
    return AnimatedBuilder(
      animation: animation,
      builder: (context, child) {
        final currentValue = begin + (end - begin) * animation.value;
        final formattedValue = currentValue.toStringAsFixed(decimalPlaces);

        return Text(
          '${prefix ?? ''}$formattedValue${suffix ?? ''}',
          style: style,
        );
      },
    );
  }

  /// رسم متحرك للموجات (Wave Effect)
  static Widget waveAnimation({
    required Widget child,
    required Animation<double> animation,
    double amplitude = 10.0,
    double frequency = 2.0,
  }) {
    return AnimatedBuilder(
      animation: animation,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(
            0,
            amplitude * math.sin(frequency * animation.value * 2 * math.pi),
          ),
          child: child,
        );
      },
      child: child,
    );
  }

  /// رسم متحرك للتوهج (Glow Effect)
  static Widget glowAnimation({
    required Widget child,
    required Animation<double> animation,
    Color glowColor = Colors.blue,
    double maxBlurRadius = 20.0,
  }) {
    return AnimatedBuilder(
      animation: animation,
      builder: (context, child) {
        final glowIntensity = animation.value;

        return Container(
          decoration: BoxDecoration(
            boxShadow: [
              BoxShadow(
                color: glowColor.withValues(alpha: glowIntensity * 0.6),
                blurRadius: maxBlurRadius * glowIntensity,
                spreadRadius: 2 * glowIntensity,
              ),
            ],
          ),
          child: child,
        );
      },
      child: child,
    );
  }

  /// رسم متحرك للجسيمات (Particle Effect)
  static Widget particleAnimation({
    required Widget child,
    required Animation<double> animation,
    int particleCount = 20,
    Color particleColor = const Color(0xFFD4AF37),
  }) {
    return Stack(
      children: [
        child,
        ...List.generate(particleCount, (index) {
          final particleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
            CurvedAnimation(
              parent: animation,
              curve: Interval(
                index / particleCount,
                1.0,
                curve: Curves.easeOut,
              ),
            ),
          );

          return AnimatedBuilder(
            animation: particleAnimation,
            builder: (context, child) {
              final angle = (index / particleCount) * 2 * math.pi;
              final distance = 50 * particleAnimation.value;
              final x = distance * math.cos(angle);
              final y = distance * math.sin(angle);

              return Positioned(
                left: x,
                top: y,
                child: Opacity(
                  opacity: 1 - particleAnimation.value,
                  child: Container(
                    width: 4,
                    height: 4,
                    decoration: BoxDecoration(
                      color: particleColor,
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
              );
            },
          );
        }),
      ],
    );
  }

  /// رسم متحرك للانزلاق المتدرج
  static Widget staggeredSlideAnimation({
    required List<Widget> children,
    required Animation<double> animation,
    Duration itemDelay = const Duration(milliseconds: 100),
    Offset slideOffset = const Offset(0, 50),
  }) {
    return Column(
      children: children.asMap().entries.map((entry) {
        final index = entry.key;
        final child = entry.value;

        final itemAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
          CurvedAnimation(
            parent: animation,
            curve: Interval(
              (index * itemDelay.inMilliseconds) / 1000.0,
              1.0,
              curve: damascusCurve,
            ),
          ),
        );

        return AnimatedBuilder(
          animation: itemAnimation,
          builder: (context, child) {
            return Transform.translate(
              offset: slideOffset * (1 - itemAnimation.value),
              child: Opacity(opacity: itemAnimation.value, child: child),
            );
          },
          child: child,
        );
      }).toList(),
    );
  }

  /// رسم متحرك للدوران الذهبي
  static Widget goldenSpinAnimation({
    required Widget child,
    required Animation<double> animation,
    int rotations = 1,
  }) {
    return AnimatedBuilder(
      animation: animation,
      builder: (context, child) {
        return Transform.rotate(
          angle: animation.value * 2 * math.pi * rotations,
          child: child,
        );
      },
      child: child,
    );
  }

  /// رسم متحرك للنبضة (Pulse Effect)
  static Widget pulseAnimation({
    required Widget child,
    required Animation<double> animation,
    double minScale = 0.95,
    double maxScale = 1.05,
  }) {
    return AnimatedBuilder(
      animation: animation,
      builder: (context, child) {
        final scale =
            minScale +
            (maxScale - minScale) *
                (0.5 + 0.5 * math.sin(animation.value * 2 * math.pi));

        return Transform.scale(scale: scale, child: child);
      },
      child: child,
    );
  }

  /// انتقال صفحة مع تأثير الكتاب
  static PageRouteBuilder<T> bookPageTransition<T>(
    Widget page, {
    Duration duration = elegant,
  }) {
    return PageRouteBuilder<T>(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: duration,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return AnimatedBuilder(
          animation: animation,
          builder: (context, child) {
            final rotationY = (1 - animation.value) * math.pi / 2;

            if (animation.value < 0.5) {
              return Transform(
                alignment: Alignment.centerRight,
                transform: Matrix4.identity()
                  ..setEntry(3, 2, 0.001)
                  ..rotateY(-rotationY),
                child: child,
              );
            } else {
              return Transform(
                alignment: Alignment.centerLeft,
                transform: Matrix4.identity()
                  ..setEntry(3, 2, 0.001)
                  ..rotateY(rotationY),
                child: child,
              );
            }
          },
          child: child,
        );
      },
    );
  }
}
