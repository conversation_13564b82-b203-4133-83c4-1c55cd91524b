import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../constants/app_colors.dart';
import '../constants/app_constants.dart';
import '../widgets/app_logo.dart';
import 'login_screen.dart';


/// شاشة الترحيب المتحركة الجذابة
class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _mainController;
  late AnimationController _logoController;
  late AnimationController _particleController;
  late AnimationController _textController;
  late AnimationController _backgroundController;

  late Animation<double> _logoScale;
  late Animation<double> _logoRotation;
  late Animation<double> _logoOpacity;
  late Animation<Offset> _logoSlide;

  late Animation<double> _titleOpacity;
  late Animation<Offset> _titleSlide;
  late Animation<double> _subtitleOpacity;
  late Animation<Offset> _subtitleSlide;

  late Animation<double> _backgroundOpacity;
  late Animation<double> _particleAnimation;

  late Animation<double> _glowAnimation;
  late Animation<Color?> _colorAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimationSequence();
  }

  void _initializeAnimations() {
    // التحكم الرئيسي
    _mainController = AnimationController(
      duration: const Duration(milliseconds: 3000),
      vsync: this,
    );

    // تحكم الشعار
    _logoController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    // تحكم الجسيمات
    _particleController = AnimationController(
      duration: const Duration(milliseconds: 4000),
      vsync: this,
    );

    // تحكم النصوص
    _textController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    // تحكم الخلفية
    _backgroundController = AnimationController(
      duration: const Duration(milliseconds: 2500),
      vsync: this,
    );

    // تحريك الشعار
    _logoScale = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _logoController,
        curve: const Interval(0.0, 0.6, curve: Curves.elasticOut),
      ),
    );

    _logoRotation = Tween<double>(begin: 0.0, end: 2 * math.pi).animate(
      CurvedAnimation(
        parent: _logoController,
        curve: const Interval(0.0, 0.8, curve: Curves.easeInOut),
      ),
    );

    _logoOpacity = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _logoController,
        curve: const Interval(0.0, 0.4, curve: Curves.easeIn),
      ),
    );

    _logoSlide = Tween<Offset>(begin: const Offset(0, -2), end: Offset.zero)
        .animate(
          CurvedAnimation(
            parent: _logoController,
            curve: const Interval(0.2, 0.8, curve: Curves.bounceOut),
          ),
        );

    // تحريك النصوص
    _titleOpacity = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _textController,
        curve: const Interval(0.0, 0.5, curve: Curves.easeIn),
      ),
    );

    _titleSlide = Tween<Offset>(begin: const Offset(-1, 0), end: Offset.zero)
        .animate(
          CurvedAnimation(
            parent: _textController,
            curve: const Interval(0.0, 0.6, curve: Curves.easeOutBack),
          ),
        );

    _subtitleOpacity = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _textController,
        curve: const Interval(0.3, 0.8, curve: Curves.easeIn),
      ),
    );

    _subtitleSlide = Tween<Offset>(begin: const Offset(1, 0), end: Offset.zero)
        .animate(
          CurvedAnimation(
            parent: _textController,
            curve: const Interval(0.3, 0.9, curve: Curves.easeOutBack),
          ),
        );

    // تحريك الخلفية والتأثيرات
    _backgroundOpacity = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _backgroundController, curve: Curves.easeInOut),
    );

    _particleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _particleController, curve: Curves.linear),
    );

    _glowAnimation = Tween<double>(begin: 0.5, end: 1.0).animate(
      CurvedAnimation(parent: _logoController, curve: Curves.easeInOut),
    );

    _colorAnimation =
        ColorTween(begin: AppColors.primary, end: AppColors.secondary).animate(
          CurvedAnimation(parent: _mainController, curve: Curves.easeInOut),
        );
  }

  void _startAnimationSequence() async {
    // بدء تحريك الخلفية
    _backgroundController.forward();

    await Future.delayed(const Duration(milliseconds: 300));

    // بدء تحريك الجسيمات
    _particleController.repeat();

    await Future.delayed(const Duration(milliseconds: 200));

    // بدء تحريك الشعار
    _logoController.forward();

    await Future.delayed(const Duration(milliseconds: 800));

    // بدء تحريك النصوص
    _textController.forward();

    // بدء التحكم الرئيسي
    _mainController.forward();

    // الانتقال للشاشة التالية
    await Future.delayed(const Duration(milliseconds: 2500));

    if (mounted) {
      Navigator.of(context).pushReplacement(
        PageRouteBuilder(
          pageBuilder: (context, animation, secondaryAnimation) =>
              const LoginScreen(),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return FadeTransition(
              opacity: animation,
              child: SlideTransition(
                position:
                    Tween<Offset>(
                      begin: const Offset(1.0, 0.0),
                      end: Offset.zero,
                    ).animate(
                      CurvedAnimation(
                        parent: animation,
                        curve: Curves.easeInOut,
                      ),
                    ),
                child: child,
              ),
            );
          },
          transitionDuration: const Duration(milliseconds: 800),
        ),
      );
    }
  }

  @override
  void dispose() {
    _mainController.dispose();
    _logoController.dispose();
    _particleController.dispose();
    _textController.dispose();
    _backgroundController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: AnimatedBuilder(
        animation: Listenable.merge([
          _mainController,
          _logoController,
          _particleController,
          _textController,
          _backgroundController,
        ]),
        builder: (context, child) {
          return Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  AppColors.primary.withValues(alpha: _backgroundOpacity.value),
                  AppColors.secondary.withValues(
                    alpha: _backgroundOpacity.value * 0.8,
                  ),
                  AppColors.primaryLight.withValues(
                    alpha: _backgroundOpacity.value * 0.6,
                  ),
                ],
              ),
            ),
            child: Stack(
              children: [
                // جسيمات متحركة في الخلفية
                _buildParticles(),

                // المحتوى الرئيسي
                Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // الشعار المتحرك
                      _buildAnimatedLogo(),

                      const SizedBox(height: 40),

                      // العنوان المتحرك
                      _buildAnimatedTitle(),

                      const SizedBox(height: 16),

                      // العنوان الفرعي المتحرك
                      _buildAnimatedSubtitle(),

                      const SizedBox(height: 60),

                      // مؤشر التحميل المتحرك
                      _buildLoadingIndicator(),
                    ],
                  ),
                ),

                // تأثير الوهج
                _buildGlowEffect(),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildParticles() {
    return Positioned.fill(
      child: CustomPaint(painter: _ParticlesPainter(_particleAnimation.value)),
    );
  }

  Widget _buildAnimatedLogo() {
    return SlideTransition(
      position: _logoSlide,
      child: FadeTransition(
        opacity: _logoOpacity,
        child: Transform.rotate(
          angle: _logoRotation.value,
          child: Transform.scale(
            scale: _logoScale.value,
            child: Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: (_colorAnimation.value ?? AppColors.primary)
                        .withValues(alpha: _glowAnimation.value * 0.6),
                    blurRadius: 30 * _glowAnimation.value,
                    spreadRadius: 10 * _glowAnimation.value,
                  ),
                ],
              ),
              child: AppLogo(size: 80),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAnimatedTitle() {
    return SlideTransition(
      position: _titleSlide,
      child: FadeTransition(
        opacity: _titleOpacity,
        child: ShaderMask(
          shaderCallback: (bounds) => LinearGradient(
            colors: [
              _colorAnimation.value ?? AppColors.primary,
              AppColors.primaryLight,
            ],
          ).createShader(bounds),
          child: Text(
            AppConstants.appNameArabic,
            style: TextStyle(
              fontSize: 32,
              fontWeight: FontWeight.bold,
              color: Colors.white,
              letterSpacing: 2,
              shadows: [
                Shadow(
                  color: Colors.black.withValues(alpha: 0.3),
                  offset: const Offset(2, 2),
                  blurRadius: 4,
                ),
              ],
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }

  Widget _buildAnimatedSubtitle() {
    return SlideTransition(
      position: _subtitleSlide,
      child: FadeTransition(
        opacity: _subtitleOpacity,
        child: Text(
          'نظام محاسبة متكامل للشركات السورية',
          style: TextStyle(
            fontSize: 16,
            color: Colors.white.withValues(alpha: 0.9),
            fontWeight: FontWeight.w500,
            letterSpacing: 1,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return FadeTransition(
      opacity: _backgroundOpacity,
      child: SizedBox(
        width: 40,
        height: 40,
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(
            Colors.white.withValues(alpha: 0.8),
          ),
          strokeWidth: 3,
        ),
      ),
    );
  }

  Widget _buildGlowEffect() {
    return Positioned.fill(
      child: IgnorePointer(
        child: Container(
          decoration: BoxDecoration(
            gradient: RadialGradient(
              center: Alignment.center,
              radius: 1.0,
              colors: [
                (_colorAnimation.value ?? AppColors.primary).withValues(
                  alpha: _glowAnimation.value * 0.1,
                ),
                Colors.transparent,
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// رسام الجسيمات المتحركة
class _ParticlesPainter extends CustomPainter {
  final double animationValue;
  final List<_Particle> particles = [];

  _ParticlesPainter(this.animationValue) {
    // إنشاء جسيمات عشوائية
    for (int i = 0; i < 50; i++) {
      particles.add(_Particle());
    }
  }

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withValues(alpha: 0.3)
      ..style = PaintingStyle.fill;

    for (final particle in particles) {
      final x = particle.x * size.width;
      final y =
          (particle.y + animationValue * particle.speed) % 1.0 * size.height;
      final radius =
          particle.size *
          (1 + math.sin(animationValue * 2 * math.pi + particle.phase) * 0.5);

      canvas.drawCircle(
        Offset(x, y),
        radius,
        paint..color = Colors.white.withValues(alpha: particle.opacity * 0.6),
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// فئة الجسيمة
class _Particle {
  final double x;
  final double y;
  final double size;
  final double speed;
  final double opacity;
  final double phase;

  _Particle()
    : x = math.Random().nextDouble(),
      y = math.Random().nextDouble(),
      size = math.Random().nextDouble() * 3 + 1,
      speed = math.Random().nextDouble() * 0.5 + 0.1,
      opacity = math.Random().nextDouble() * 0.8 + 0.2,
      phase = math.Random().nextDouble() * 2 * math.pi;
}
