import 'package:flutter/material.dart';
import '../constants/app_colors.dart';

/// أيقونة محسنة مع تباين أفضل وتأثيرات بصرية
class EnhancedIcon extends StatelessWidget {
  final IconData icon;
  final double? size;
  final Color? color;
  final bool highContrast;
  final bool withShadow;
  final bool withBackground;
  final Color? backgroundColor;
  final double? backgroundOpacity;
  final BorderRadius? borderRadius;
  final EdgeInsets? padding;
  final VoidCallback? onTap;
  final String? tooltip;
  final bool animated;

  const EnhancedIcon(
    this.icon, {
    super.key,
    this.size,
    this.color,
    this.highContrast = false,
    this.withShadow = false,
    this.withBackground = false,
    this.backgroundColor,
    this.backgroundOpacity,
    this.borderRadius,
    this.padding,
    this.onTap,
    this.tooltip,
    this.animated = false,
  });

  /// أيقونة صغيرة محسنة
  const EnhancedIcon.small(
    this.icon, {
    super.key,
    this.color,
    this.highContrast = false,
    this.withShadow = false,
    this.withBackground = false,
    this.backgroundColor,
    this.backgroundOpacity,
    this.borderRadius,
    this.padding,
    this.onTap,
    this.tooltip,
    this.animated = false,
  }) : size = 16;

  /// أيقونة متوسطة محسنة
  const EnhancedIcon.medium(
    this.icon, {
    super.key,
    this.color,
    this.highContrast = false,
    this.withShadow = false,
    this.withBackground = false,
    this.backgroundColor,
    this.backgroundOpacity,
    this.borderRadius,
    this.padding,
    this.onTap,
    this.tooltip,
    this.animated = false,
  }) : size = 24;

  /// أيقونة كبيرة محسنة
  const EnhancedIcon.large(
    this.icon, {
    super.key,
    this.color,
    this.highContrast = false,
    this.withShadow = false,
    this.withBackground = false,
    this.backgroundColor,
    this.backgroundOpacity,
    this.borderRadius,
    this.padding,
    this.onTap,
    this.tooltip,
    this.animated = false,
  }) : size = 32;

  /// أيقونة كبيرة جداً محسنة
  const EnhancedIcon.extraLarge(
    this.icon, {
    super.key,
    this.color,
    this.highContrast = false,
    this.withShadow = false,
    this.withBackground = false,
    this.backgroundColor,
    this.backgroundOpacity,
    this.borderRadius,
    this.padding,
    this.onTap,
    this.tooltip,
    this.animated = false,
  }) : size = 48;

  @override
  Widget build(BuildContext context) {
    final effectiveSize = size ?? 24;
    final effectiveColor = _getEffectiveColor();
    
    Widget iconWidget = Icon(
      icon,
      size: effectiveSize,
      color: effectiveColor,
      shadows: withShadow ? _buildShadows() : null,
    );

    if (withBackground) {
      iconWidget = Container(
        padding: padding ?? EdgeInsets.all(effectiveSize * 0.3),
        decoration: BoxDecoration(
          color: _getBackgroundColor(),
          borderRadius: borderRadius ?? BorderRadius.circular(effectiveSize * 0.2),
          boxShadow: withShadow ? _buildContainerShadows() : null,
        ),
        child: iconWidget,
      );
    } else if (padding != null) {
      iconWidget = Padding(
        padding: padding!,
        child: iconWidget,
      );
    }

    if (animated) {
      iconWidget = _AnimatedIcon(child: iconWidget);
    }

    if (onTap != null) {
      iconWidget = InkWell(
        onTap: onTap,
        borderRadius: borderRadius ?? BorderRadius.circular(effectiveSize * 0.2),
        child: iconWidget,
      );
    }

    if (tooltip != null) {
      iconWidget = Tooltip(
        message: tooltip!,
        child: iconWidget,
      );
    }

    return iconWidget;
  }

  Color _getEffectiveColor() {
    if (color != null) return color!;
    
    if (highContrast) {
      return AppColors.textPrimary;
    }
    
    return AppColors.textSecondary;
  }

  Color _getBackgroundColor() {
    if (backgroundColor != null) {
      return backgroundColor!.withValues(
        alpha: backgroundOpacity ?? 0.1,
      );
    }
    
    final baseColor = color ?? AppColors.primary;
    return baseColor.withValues(alpha: backgroundOpacity ?? 0.1);
  }

  List<Shadow> _buildShadows() {
    return [
      Shadow(
        color: Colors.black.withValues(alpha: 0.25),
        offset: const Offset(0, 1),
        blurRadius: 2,
      ),
    ];
  }

  List<BoxShadow> _buildContainerShadows() {
    return [
      BoxShadow(
        color: Colors.black.withValues(alpha: 0.1),
        offset: const Offset(0, 2),
        blurRadius: 4,
        spreadRadius: 1,
      ),
    ];
  }
}

/// أيقونة متحركة
class _AnimatedIcon extends StatefulWidget {
  final Widget child;

  const _AnimatedIcon({required this.child});

  @override
  State<_AnimatedIcon> createState() => _AnimatedIconState();
}

class _AnimatedIconState extends State<_AnimatedIcon>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => _controller.forward(),
      onExit: (_) => _controller.reverse(),
      child: ScaleTransition(
        scale: _scaleAnimation,
        child: widget.child,
      ),
    );
  }
}

/// أيقونة حالة مع ألوان محددة
class StatusIcon extends StatelessWidget {
  final IconData icon;
  final StatusType status;
  final double? size;
  final bool withBackground;
  final String? tooltip;

  const StatusIcon({
    super.key,
    required this.icon,
    required this.status,
    this.size,
    this.withBackground = false,
    this.tooltip,
  });

  @override
  Widget build(BuildContext context) {
    return EnhancedIcon(
      icon,
      size: size,
      color: _getStatusColor(),
      withBackground: withBackground,
      backgroundColor: _getStatusColor(),
      backgroundOpacity: 0.1,
      tooltip: tooltip,
      highContrast: true,
    );
  }

  Color _getStatusColor() {
    switch (status) {
      case StatusType.success:
        return AppColors.success;
      case StatusType.warning:
        return AppColors.warning;
      case StatusType.error:
        return AppColors.error;
      case StatusType.info:
        return AppColors.info;
      case StatusType.primary:
        return AppColors.primary;
      case StatusType.secondary:
        return AppColors.secondary;
    }
  }
}

/// أنواع الحالات
enum StatusType {
  success,
  warning,
  error,
  info,
  primary,
  secondary,
}

/// أيقونة تفاعلية مع تأثيرات
class InteractiveIcon extends StatefulWidget {
  final IconData icon;
  final IconData? activeIcon;
  final double? size;
  final Color? color;
  final Color? activeColor;
  final bool isActive;
  final VoidCallback? onTap;
  final String? tooltip;
  final bool withRipple;

  const InteractiveIcon({
    super.key,
    required this.icon,
    this.activeIcon,
    this.size,
    this.color,
    this.activeColor,
    this.isActive = false,
    this.onTap,
    this.tooltip,
    this.withRipple = true,
  });

  @override
  State<InteractiveIcon> createState() => _InteractiveIconState();
}

class _InteractiveIconState extends State<InteractiveIcon>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotationAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.elasticOut,
    ));
    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 0.1,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    if (widget.isActive) {
      _controller.forward();
    }
  }

  @override
  void didUpdateWidget(InteractiveIcon oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isActive != oldWidget.isActive) {
      if (widget.isActive) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final effectiveIcon = widget.isActive && widget.activeIcon != null
        ? widget.activeIcon!
        : widget.icon;
    
    final effectiveColor = widget.isActive && widget.activeColor != null
        ? widget.activeColor!
        : (widget.color ?? AppColors.textSecondary);

    Widget iconWidget = AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Transform.rotate(
            angle: _rotationAnimation.value,
            child: Icon(
              effectiveIcon,
              size: widget.size ?? 24,
              color: effectiveColor,
            ),
          ),
        );
      },
    );

    if (widget.onTap != null) {
      iconWidget = InkWell(
        onTap: widget.onTap,
        borderRadius: BorderRadius.circular((widget.size ?? 24) * 0.5),
        splashColor: widget.withRipple 
            ? effectiveColor.withValues(alpha: 0.2)
            : Colors.transparent,
        child: Padding(
          padding: EdgeInsets.all((widget.size ?? 24) * 0.2),
          child: iconWidget,
        ),
      );
    }

    if (widget.tooltip != null) {
      iconWidget = Tooltip(
        message: widget.tooltip!,
        child: iconWidget,
      );
    }

    return iconWidget;
  }
}
