import 'package:flutter/material.dart';
import '../constants/revolutionary_design_colors.dart';
import '../constants/app_animations.dart';

/// مكونات واجهة مستخدم ثورية ومبتكرة
class RevolutionaryUI {
  /// بطاقة ثلاثية الأبعاد مع تأثيرات متقدمة
  static Widget floating3DCard({
    required Widget child,
    required String title,
    String? subtitle,
    IconData? icon,
    VoidCallback? onTap,
    Color? accentColor,
    double elevation = 12.0,
    bool isGlowing = false,
  }) {
    return Container(
      margin: const EdgeInsets.all(8),
      child: Material(
        elevation: elevation,
        borderRadius: BorderRadius.circular(20),
        shadowColor:
            accentColor?.withValues(alpha: 0.3) ??
            RevolutionaryColors.shadowBlue,
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            gradient: isGlowing
                ? RevolutionaryColors.goldGradient
                : RevolutionaryColors.jasmineGradient,
            border: Border.all(
              color: accentColor ?? RevolutionaryColors.damascusSky,
              width: 2,
            ),
            boxShadow: [
              if (isGlowing) ...[
                BoxShadow(
                  color: RevolutionaryColors.syrianGold.withValues(alpha: 0.4),
                  blurRadius: 20,
                  spreadRadius: 2,
                ),
              ],
              BoxShadow(
                color: RevolutionaryColors.shadowMedium,
                blurRadius: 15,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            borderRadius: BorderRadius.circular(20),
            child: InkWell(
              borderRadius: BorderRadius.circular(20),
              onTap: onTap,
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (icon != null) ...[
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          gradient: RevolutionaryColors.damascusGradient,
                          borderRadius: BorderRadius.circular(15),
                          boxShadow: [
                            RevolutionaryColors.createGlowShadow(
                              color:
                                  accentColor ??
                                  RevolutionaryColors.damascusSky,
                            ),
                          ],
                        ),
                        child: Icon(
                          icon,
                          color: RevolutionaryColors.textOnDark,
                          size: 28,
                        ),
                      ),
                      const SizedBox(height: 16),
                    ],
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: RevolutionaryColors.textPrimary,
                        fontFamily: 'Cairo',
                      ),
                    ),
                    if (subtitle != null) ...[
                      const SizedBox(height: 8),
                      Text(
                        subtitle,
                        style: const TextStyle(
                          fontSize: 14,
                          color: RevolutionaryColors.textSecondary,
                          fontFamily: 'Cairo',
                        ),
                      ),
                    ],
                    const SizedBox(height: 12),
                    child,
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// زر متحرك مع تأثيرات ضوئية
  static Widget glowingButton({
    required String text,
    required VoidCallback onPressed,
    IconData? icon,
    Color? color,
    bool isLoading = false,
    bool isPrimary = true,
    double width = double.infinity,
  }) {
    final buttonColor =
        color ??
        (isPrimary
            ? RevolutionaryColors.damascusSky
            : RevolutionaryColors.syrianGold);

    return Container(
      width: width,
      height: 56,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(28),
        gradient: isPrimary
            ? RevolutionaryColors.damascusGradient
            : RevolutionaryColors.goldGradient,
        boxShadow: [
          RevolutionaryColors.createGlowShadow(
            color: buttonColor,
            blurRadius: 12,
            spreadRadius: 1,
          ),
          const BoxShadow(
            color: RevolutionaryColors.shadowMedium,
            blurRadius: 8,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(28),
        child: InkWell(
          borderRadius: BorderRadius.circular(28),
          onTap: isLoading ? null : onPressed,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (isLoading) ...[
                  const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        RevolutionaryColors.textOnDark,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                ] else if (icon != null) ...[
                  Icon(icon, color: RevolutionaryColors.textOnDark, size: 22),
                  const SizedBox(width: 12),
                ],
                Text(
                  text,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: RevolutionaryColors.textOnDark,
                    fontFamily: 'Cairo',
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// حقل إدخال مع تصميم عائم
  static Widget floatingTextField({
    required String label,
    String? hint,
    IconData? prefixIcon,
    IconData? suffixIcon,
    VoidCallback? onSuffixTap,
    TextEditingController? controller,
    Function(String)? onChanged,
    bool isPassword = false,
    bool isRequired = false,
    String? errorText,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        gradient: RevolutionaryColors.jasmineGradient,
        border: Border.all(
          color: errorText != null
              ? RevolutionaryColors.errorCoral
              : RevolutionaryColors.borderLight,
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: RevolutionaryColors.shadowSoft,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextFormField(
        controller: controller,
        onChanged: onChanged,
        obscureText: isPassword,
        style: const TextStyle(
          fontSize: 16,
          color: RevolutionaryColors.textPrimary,
          fontFamily: 'Cairo',
        ),
        decoration: InputDecoration(
          labelText: label + (isRequired ? ' *' : ''),
          hintText: hint,
          prefixIcon: prefixIcon != null
              ? Icon(prefixIcon, color: RevolutionaryColors.damascusSky)
              : null,
          suffixIcon: suffixIcon != null
              ? IconButton(
                  icon: Icon(
                    suffixIcon,
                    color: RevolutionaryColors.damascusSky,
                  ),
                  onPressed: onSuffixTap,
                )
              : null,
          border: InputBorder.none,
          contentPadding: const EdgeInsets.all(20),
          labelStyle: TextStyle(
            color: RevolutionaryColors.textSecondary,
            fontFamily: 'Cairo',
            fontWeight: FontWeight.w500,
          ),
          hintStyle: const TextStyle(
            color: RevolutionaryColors.textHint,
            fontFamily: 'Cairo',
          ),
          errorText: errorText,
          errorStyle: const TextStyle(
            color: RevolutionaryColors.errorCoral,
            fontFamily: 'Cairo',
          ),
        ),
      ),
    );
  }

  /// شريط تنقل عائم مع تأثيرات
  static Widget floatingNavigationBar({
    required List<NavigationItem> items,
    required int currentIndex,
    required Function(int) onTap,
  }) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(25),
        gradient: RevolutionaryColors.damascusGradient,
        boxShadow: [
          RevolutionaryColors.createGlowShadow(
            color: RevolutionaryColors.damascusSky,
            blurRadius: 15,
            spreadRadius: 2,
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: items.asMap().entries.map((entry) {
          final index = entry.key;
          final item = entry.value;
          final isSelected = index == currentIndex;

          return GestureDetector(
            onTap: () => onTap(index),
            child: AnimatedContainer(
              duration: AppAnimations.normal,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                color: isSelected
                    ? RevolutionaryColors.syrianGold.withValues(alpha: 0.3)
                    : Colors.transparent,
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    item.icon,
                    color: isSelected
                        ? RevolutionaryColors.syrianGold
                        : RevolutionaryColors.textOnDark,
                    size: 24,
                  ),
                  if (isSelected) ...[
                    const SizedBox(width: 8),
                    Text(
                      item.label,
                      style: const TextStyle(
                        color: RevolutionaryColors.syrianGold,
                        fontWeight: FontWeight.w600,
                        fontFamily: 'Cairo',
                      ),
                    ),
                  ],
                ],
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  /// بطاقة إحصائيات متحركة
  static Widget animatedStatsCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    String? subtitle,
    double? percentage,
    bool isIncreasing = true,
  }) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: RevolutionaryColors.jasmineGradient,
        border: Border.all(color: color.withValues(alpha: 0.3), width: 2),
        boxShadow: [
          RevolutionaryColors.createGlowShadow(color: color, blurRadius: 10),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(icon, color: color, size: 24),
              ),
              if (percentage != null)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: isIncreasing
                        ? RevolutionaryColors.successGlow.withValues(alpha: 0.1)
                        : RevolutionaryColors.errorCoral.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        isIncreasing ? Icons.trending_up : Icons.trending_down,
                        size: 16,
                        color: isIncreasing
                            ? RevolutionaryColors.successGlow
                            : RevolutionaryColors.errorCoral,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${percentage.toStringAsFixed(1)}%',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: isIncreasing
                              ? RevolutionaryColors.successGlow
                              : RevolutionaryColors.errorCoral,
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: const TextStyle(
              fontSize: 14,
              color: RevolutionaryColors.textSecondary,
              fontFamily: 'Cairo',
            ),
          ),
          const SizedBox(height: 8),
          TweenAnimationBuilder<double>(
            tween: Tween<double>(begin: 0, end: 1),
            duration: const Duration(milliseconds: 1500),
            curve: Curves.elasticOut,
            builder: (context, animationValue, child) {
              return Text(
                value,
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: color,
                  fontFamily: 'Cairo',
                ),
              );
            },
          ),
          if (subtitle != null) ...[
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: const TextStyle(
                fontSize: 12,
                color: RevolutionaryColors.textHint,
                fontFamily: 'Cairo',
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// قائمة منسدلة مع تصميم عائم
  static Widget floatingDropdown<T>({
    required String label,
    required T? value,
    required List<DropdownMenuItem<T>> items,
    required Function(T?) onChanged,
    IconData? prefixIcon,
    bool isRequired = false,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        gradient: RevolutionaryColors.jasmineGradient,
        border: Border.all(color: RevolutionaryColors.borderLight, width: 1.5),
        boxShadow: [
          BoxShadow(
            color: RevolutionaryColors.shadowSoft,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: DropdownButtonFormField<T>(
        value: value,
        items: items,
        onChanged: onChanged,
        decoration: InputDecoration(
          labelText: label + (isRequired ? ' *' : ''),
          prefixIcon: prefixIcon != null
              ? Icon(prefixIcon, color: RevolutionaryColors.damascusSky)
              : null,
          border: InputBorder.none,
          contentPadding: const EdgeInsets.all(20),
          labelStyle: const TextStyle(
            color: RevolutionaryColors.textSecondary,
            fontFamily: 'Cairo',
            fontWeight: FontWeight.w500,
          ),
        ),
        style: const TextStyle(
          fontSize: 16,
          color: RevolutionaryColors.textPrimary,
          fontFamily: 'Cairo',
        ),
        dropdownColor: RevolutionaryColors.backgroundCard,
        borderRadius: BorderRadius.circular(16),
      ),
    );
  }
}

/// عنصر التنقل
class NavigationItem {
  final IconData icon;
  final String label;

  NavigationItem({required this.icon, required this.label});
}
