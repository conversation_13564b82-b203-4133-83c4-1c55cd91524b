/// خدمة التنبيهات الذكية للمخزون
/// تدير تنبيهات المخزون المختلفة مع إعدادات مخصصة لكل موقع
library;

import 'dart:convert';

import '../database/database_helper.dart';
import '../services/logging_service.dart';
import '../services/audit_service.dart';

import '../services/inventory_valuation_service.dart';
import '../constants/app_constants.dart';

/// أنواع التنبيهات
enum AlertType {
  lowStock('low_stock', 'مخزون منخفض'),
  outOfStock('out_of_stock', 'نفاد المخزون'),
  overStock('over_stock', 'مخزون زائد'),
  expiringSoon('expiring_soon', 'انتهاء صلاحية قريب'),
  expired('expired', 'منتهي الصلاحية'),
  slowMoving('slow_moving', 'بطيء الحركة'),
  fastMoving('fast_moving', 'سريع الحركة'),
  negativeStock('negative_stock', 'مخزون سالب');

  const AlertType(this.code, this.displayName);
  final String code;
  final String displayName;

  static AlertType fromCode(String code) {
    return AlertType.values.firstWhere(
      (type) => type.code == code,
      orElse: () => AlertType.lowStock,
    );
  }
}

/// مستويات الأولوية
enum AlertPriority {
  low('low', 'منخفض'),
  medium('medium', 'متوسط'),
  high('high', 'عالي'),
  critical('critical', 'حرج');

  const AlertPriority(this.code, this.displayName);
  final String code;
  final String displayName;

  static AlertPriority fromCode(String code) {
    return AlertPriority.values.firstWhere(
      (priority) => priority.code == code,
      orElse: () => AlertPriority.medium,
    );
  }
}

/// نموذج التنبيه
class InventoryAlert {
  final int? id;
  final AlertType alertType;
  final AlertPriority priority;
  final int itemId;
  final int? warehouseId;
  final int? locationId;
  final String title;
  final String message;
  final Map<String, dynamic> data;
  final bool isRead;
  final bool isResolved;
  final DateTime createdAt;
  final DateTime? resolvedAt;

  InventoryAlert({
    this.id,
    required this.alertType,
    required this.priority,
    required this.itemId,
    this.warehouseId,
    this.locationId,
    required this.title,
    required this.message,
    this.data = const {},
    this.isRead = false,
    this.isResolved = false,
    DateTime? createdAt,
    this.resolvedAt,
  }) : createdAt = createdAt ?? DateTime.now();

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'alert_type': alertType.code,
      'priority': priority.code,
      'item_id': itemId,
      'warehouse_id': warehouseId,
      'location_id': locationId,
      'title': title,
      'message': message,
      'data': data.isNotEmpty ? data.toString() : null,
      'is_read': isRead ? 1 : 0,
      'is_resolved': isResolved ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'resolved_at': resolvedAt?.toIso8601String(),
    };
  }

  factory InventoryAlert.fromMap(Map<String, dynamic> map) {
    return InventoryAlert(
      id: map['id']?.toInt(),
      alertType: AlertType.fromCode(map['alert_type'] ?? 'low_stock'),
      priority: AlertPriority.fromCode(map['priority'] ?? 'medium'),
      itemId: map['item_id']?.toInt() ?? 0,
      warehouseId: map['warehouse_id']?.toInt(),
      locationId: map['location_id']?.toInt(),
      title: map['title'] ?? '',
      message: map['message'] ?? '',
      data: _parseJsonData(map['data']),
      isRead: (map['is_read'] ?? 0) == 1,
      isResolved: (map['is_resolved'] ?? 0) == 1,
      createdAt: DateTime.parse(
        map['created_at'] ?? DateTime.now().toIso8601String(),
      ),
      resolvedAt: map['resolved_at'] != null
          ? DateTime.parse(map['resolved_at'])
          : null,
    );
  }

  /// تحليل بيانات JSON
  static Map<String, dynamic> _parseJsonData(dynamic data) {
    if (data == null) return {};
    if (data is Map<String, dynamic>) return data;
    if (data is String) {
      try {
        final decoded = jsonDecode(data);
        return decoded is Map<String, dynamic> ? decoded : {};
      } catch (e) {
        return {};
      }
    }
    return {};
  }

  InventoryAlert copyWith({
    int? id,
    AlertType? alertType,
    AlertPriority? priority,
    int? itemId,
    int? warehouseId,
    int? locationId,
    String? title,
    String? message,
    Map<String, dynamic>? data,
    bool? isRead,
    bool? isResolved,
    DateTime? createdAt,
    DateTime? resolvedAt,
  }) {
    return InventoryAlert(
      id: id ?? this.id,
      alertType: alertType ?? this.alertType,
      priority: priority ?? this.priority,
      itemId: itemId ?? this.itemId,
      warehouseId: warehouseId ?? this.warehouseId,
      locationId: locationId ?? this.locationId,
      title: title ?? this.title,
      message: message ?? this.message,
      data: data ?? this.data,
      isRead: isRead ?? this.isRead,
      isResolved: isResolved ?? this.isResolved,
      createdAt: createdAt ?? this.createdAt,
      resolvedAt: resolvedAt ?? this.resolvedAt,
    );
  }
}

/// إعدادات التنبيهات
class AlertSettings {
  final int? id;
  final int? itemId;
  final int? warehouseId;
  final int? locationId;
  final AlertType alertType;
  final bool isEnabled;
  final Map<String, dynamic> thresholds;
  final DateTime createdAt;
  final DateTime updatedAt;

  AlertSettings({
    this.id,
    this.itemId,
    this.warehouseId,
    this.locationId,
    required this.alertType,
    this.isEnabled = true,
    this.thresholds = const {},
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'item_id': itemId,
      'warehouse_id': warehouseId,
      'location_id': locationId,
      'alert_type': alertType.code,
      'is_enabled': isEnabled ? 1 : 0,
      'thresholds': thresholds.isNotEmpty ? thresholds.toString() : null,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  factory AlertSettings.fromMap(Map<String, dynamic> map) {
    return AlertSettings(
      id: map['id']?.toInt(),
      itemId: map['item_id']?.toInt(),
      warehouseId: map['warehouse_id']?.toInt(),
      locationId: map['location_id']?.toInt(),
      alertType: AlertType.fromCode(map['alert_type'] ?? 'low_stock'),
      isEnabled: (map['is_enabled'] ?? 1) == 1,
      thresholds: _parseJsonData(map['thresholds']),
      createdAt: DateTime.parse(
        map['created_at'] ?? DateTime.now().toIso8601String(),
      ),
      updatedAt: DateTime.parse(
        map['updated_at'] ?? DateTime.now().toIso8601String(),
      ),
    );
  }

  /// تحليل بيانات JSON
  static Map<String, dynamic> _parseJsonData(dynamic data) {
    if (data == null) return {};
    if (data is Map<String, dynamic>) return data;
    if (data is String) {
      try {
        final decoded = jsonDecode(data);
        return decoded is Map<String, dynamic> ? decoded : {};
      } catch (e) {
        return {};
      }
    }
    return {};
  }
}

class InventoryAlertsService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final InventoryValuationService _valuationService =
      InventoryValuationService();

  /// إنشاء جداول التنبيهات
  Future<void> createTables() async {
    final db = await _databaseHelper.database;

    await db.transaction((txn) async {
      // جدول التنبيهات
      await txn.execute('''
        CREATE TABLE IF NOT EXISTS inventory_alerts (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          alert_type TEXT NOT NULL,
          priority TEXT NOT NULL,
          item_id INTEGER NOT NULL,
          warehouse_id INTEGER,
          location_id INTEGER,
          title TEXT NOT NULL,
          message TEXT NOT NULL,
          data TEXT,
          is_read INTEGER NOT NULL DEFAULT 0,
          is_resolved INTEGER NOT NULL DEFAULT 0,
          created_at TEXT NOT NULL,
          resolved_at TEXT,
          FOREIGN KEY (item_id) REFERENCES ${AppConstants.itemsTable} (id),
          FOREIGN KEY (warehouse_id) REFERENCES warehouses (id),
          FOREIGN KEY (location_id) REFERENCES warehouse_locations (id)
        )
      ''');

      // جدول إعدادات التنبيهات
      await txn.execute('''
        CREATE TABLE IF NOT EXISTS alert_settings (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          item_id INTEGER,
          warehouse_id INTEGER,
          location_id INTEGER,
          alert_type TEXT NOT NULL,
          is_enabled INTEGER NOT NULL DEFAULT 1,
          thresholds TEXT,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL,
          FOREIGN KEY (item_id) REFERENCES ${AppConstants.itemsTable} (id),
          FOREIGN KEY (warehouse_id) REFERENCES warehouses (id),
          FOREIGN KEY (location_id) REFERENCES warehouse_locations (id)
        )
      ''');

      // إنشاء فهارس للأداء
      await txn.execute(
        'CREATE INDEX IF NOT EXISTS idx_inventory_alerts_item_id ON inventory_alerts(item_id)',
      );
      await txn.execute(
        'CREATE INDEX IF NOT EXISTS idx_inventory_alerts_type ON inventory_alerts(alert_type)',
      );
      await txn.execute(
        'CREATE INDEX IF NOT EXISTS idx_inventory_alerts_priority ON inventory_alerts(priority)',
      );
      await txn.execute(
        'CREATE INDEX IF NOT EXISTS idx_inventory_alerts_read ON inventory_alerts(is_read)',
      );
      await txn.execute(
        'CREATE INDEX IF NOT EXISTS idx_inventory_alerts_resolved ON inventory_alerts(is_resolved)',
      );
      await txn.execute(
        'CREATE INDEX IF NOT EXISTS idx_alert_settings_item_id ON alert_settings(item_id)',
      );
      await txn.execute(
        'CREATE INDEX IF NOT EXISTS idx_alert_settings_type ON alert_settings(alert_type)',
      );
    });

    LoggingService.info(
      'تم إنشاء جداول التنبيهات',
      category: 'InventoryAlertsService',
    );
  }

  /// فحص وإنشاء التنبيهات
  Future<List<InventoryAlert>> checkAndCreateAlerts() async {
    try {
      final alerts = <InventoryAlert>[];

      // فحص المخزون المنخفض
      alerts.addAll(await _checkLowStockAlerts());

      // فحص نفاد المخزون
      alerts.addAll(await _checkOutOfStockAlerts());

      // فحص انتهاء الصلاحية
      alerts.addAll(await _checkExpiryAlerts());

      // فحص المخزون السالب
      alerts.addAll(await _checkNegativeStockAlerts());

      LoggingService.info(
        'تم فحص التنبيهات',
        category: 'InventoryAlertsService',
        data: {'alerts_created': alerts.length},
      );

      return alerts;
    } catch (e) {
      LoggingService.error(
        'خطأ في فحص التنبيهات',
        category: 'InventoryAlertsService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// فحص تنبيهات المخزون المنخفض
  Future<List<InventoryAlert>> _checkLowStockAlerts() async {
    try {
      final db = await _databaseHelper.database;
      final alerts = <InventoryAlert>[];

      // البحث عن الأصناف ذات المخزون المنخفض
      final result = await db.rawQuery('''
        SELECT ils.*, i.name as item_name, i.code as item_code,
               i.min_quantity, wl.name as location_name, w.name as warehouse_name
        FROM item_location_stock ils
        JOIN ${AppConstants.itemsTable} i ON ils.item_id = i.id
        JOIN warehouse_locations wl ON ils.location_id = wl.id
        JOIN warehouses w ON ils.warehouse_id = w.id
        WHERE ils.available_quantity <= i.min_quantity
        AND i.min_quantity > 0
        AND ils.available_quantity > 0
      ''');

      for (final row in result) {
        final itemId = row['item_id'] as int;
        final warehouseId = row['warehouse_id'] as int;
        final locationId = row['location_id'] as int;

        // التحقق من عدم وجود تنبيه مماثل غير محلول
        final existingAlert = await _getExistingAlert(
          itemId: itemId,
          locationId: locationId,
          alertType: AlertType.lowStock,
        );

        if (existingAlert == null) {
          final alert = InventoryAlert(
            alertType: AlertType.lowStock,
            priority: AlertPriority.medium,
            itemId: itemId,
            warehouseId: warehouseId,
            locationId: locationId,
            title: 'مخزون منخفض',
            message:
                'الصنف ${row['item_name']} في ${row['location_name']} وصل للحد الأدنى',
            data: {
              'current_quantity': row['available_quantity'],
              'min_quantity': row['min_quantity'],
              'item_code': row['item_code'],
              'location_name': row['location_name'],
              'warehouse_name': row['warehouse_name'],
            },
          );

          await _createAlert(alert);
          alerts.add(alert);
        }
      }

      return alerts;
    } catch (e) {
      LoggingService.error(
        'خطأ في فحص تنبيهات المخزون المنخفض',
        category: 'InventoryAlertsService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// فحص تنبيهات نفاد المخزون
  Future<List<InventoryAlert>> _checkOutOfStockAlerts() async {
    try {
      final db = await _databaseHelper.database;
      final alerts = <InventoryAlert>[];

      // البحث عن الأصناف المنفدة
      final result = await db.rawQuery('''
        SELECT ils.*, i.name as item_name, i.code as item_code,
               wl.name as location_name, w.name as warehouse_name
        FROM item_location_stock ils
        JOIN ${AppConstants.itemsTable} i ON ils.item_id = i.id
        JOIN warehouse_locations wl ON ils.location_id = wl.id
        JOIN warehouses w ON ils.warehouse_id = w.id
        WHERE ils.available_quantity <= 0
        AND i.is_active = 1
      ''');

      for (final row in result) {
        final itemId = row['item_id'] as int;
        final warehouseId = row['warehouse_id'] as int;
        final locationId = row['location_id'] as int;

        final existingAlert = await _getExistingAlert(
          itemId: itemId,
          locationId: locationId,
          alertType: AlertType.outOfStock,
        );

        if (existingAlert == null) {
          final alert = InventoryAlert(
            alertType: AlertType.outOfStock,
            priority: AlertPriority.high,
            itemId: itemId,
            warehouseId: warehouseId,
            locationId: locationId,
            title: 'نفاد المخزون',
            message: 'الصنف ${row['item_name']} نفد من ${row['location_name']}',
            data: {
              'current_quantity': row['available_quantity'],
              'item_code': row['item_code'],
              'location_name': row['location_name'],
              'warehouse_name': row['warehouse_name'],
            },
          );

          await _createAlert(alert);
          alerts.add(alert);
        }
      }

      return alerts;
    } catch (e) {
      LoggingService.error(
        'خطأ في فحص تنبيهات نفاد المخزون',
        category: 'InventoryAlertsService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// فحص تنبيهات انتهاء الصلاحية
  Future<List<InventoryAlert>> _checkExpiryAlerts() async {
    try {
      final alerts = <InventoryAlert>[];

      // الحصول على الطبقات المنتهية الصلاحية أو القريبة من الانتهاء
      final expiringLayers = await _valuationService.getExpiringLayers(
        daysToExpiry: 30,
      );

      for (final layer in expiringLayers) {
        final isExpired = layer.expiryDate!.isBefore(DateTime.now());

        final existingAlert = await _getExistingAlert(
          itemId: layer.itemId,
          locationId: layer.locationId,
          alertType: isExpired ? AlertType.expired : AlertType.expiringSoon,
        );

        if (existingAlert == null) {
          final isExpired = layer.expiryDate!.isBefore(DateTime.now());
          final daysToExpiry = layer.expiryDate!
              .difference(DateTime.now())
              .inDays;

          final alert = InventoryAlert(
            alertType: isExpired ? AlertType.expired : AlertType.expiringSoon,
            priority: isExpired ? AlertPriority.critical : AlertPriority.high,
            itemId: layer.itemId,
            locationId: layer.locationId,
            title: isExpired ? 'منتهي الصلاحية' : 'انتهاء صلاحية قريب',
            message: isExpired
                ? 'دفعة منتهية الصلاحية'
                : 'دفعة ستنتهي صلاحيتها خلال $daysToExpiry يوم',
            data: {
              'batch_number': layer.batchNumber,
              'expiry_date': layer.expiryDate?.toIso8601String(),
              'remaining_quantity': layer.remainingQuantity,
              'days_to_expiry': daysToExpiry,
            },
          );

          await _createAlert(alert);
          alerts.add(alert);
        }
      }

      return alerts;
    } catch (e) {
      LoggingService.error(
        'خطأ في فحص تنبيهات انتهاء الصلاحية',
        category: 'InventoryAlertsService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// فحص تنبيهات المخزون السالب
  Future<List<InventoryAlert>> _checkNegativeStockAlerts() async {
    try {
      final db = await _databaseHelper.database;
      final alerts = <InventoryAlert>[];

      // البحث عن المخزون السالب
      final result = await db.rawQuery('''
        SELECT ils.*, i.name as item_name, i.code as item_code,
               wl.name as location_name, w.name as warehouse_name
        FROM item_location_stock ils
        JOIN ${AppConstants.itemsTable} i ON ils.item_id = i.id
        JOIN warehouse_locations wl ON ils.location_id = wl.id
        JOIN warehouses w ON ils.warehouse_id = w.id
        WHERE ils.quantity < 0
      ''');

      for (final row in result) {
        final itemId = row['item_id'] as int;
        final warehouseId = row['warehouse_id'] as int;
        final locationId = row['location_id'] as int;

        final existingAlert = await _getExistingAlert(
          itemId: itemId,
          locationId: locationId,
          alertType: AlertType.negativeStock,
        );

        if (existingAlert == null) {
          final alert = InventoryAlert(
            alertType: AlertType.negativeStock,
            priority: AlertPriority.critical,
            itemId: itemId,
            warehouseId: warehouseId,
            locationId: locationId,
            title: 'مخزون سالب',
            message:
                'الصنف ${row['item_name']} له رصيد سالب في ${row['location_name']}',
            data: {
              'current_quantity': row['quantity'],
              'item_code': row['item_code'],
              'location_name': row['location_name'],
              'warehouse_name': row['warehouse_name'],
            },
          );

          await _createAlert(alert);
          alerts.add(alert);
        }
      }

      return alerts;
    } catch (e) {
      LoggingService.error(
        'خطأ في فحص تنبيهات المخزون السالب',
        category: 'InventoryAlertsService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// إنشاء تنبيه جديد
  Future<int> _createAlert(InventoryAlert alert) async {
    try {
      final db = await _databaseHelper.database;

      final id = await db.insert('inventory_alerts', alert.toMap());

      await AuditService.log(
        action: 'alert_created',
        entityType: 'inventory_alert',
        entityId: id,
        description: 'تم إنشاء تنبيه: ${alert.title}',
        category: 'Inventory',
      );

      LoggingService.info(
        'تم إنشاء تنبيه جديد',
        category: 'InventoryAlertsService',
        data: {
          'alert_id': id,
          'alert_type': alert.alertType.code,
          'item_id': alert.itemId,
          'priority': alert.priority.code,
        },
      );

      return id;
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء التنبيه',
        category: 'InventoryAlertsService',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// البحث عن تنبيه موجود
  Future<InventoryAlert?> _getExistingAlert({
    required int itemId,
    int? locationId,
    required AlertType alertType,
  }) async {
    try {
      final db = await _databaseHelper.database;

      String whereClause = 'item_id = ? AND alert_type = ? AND is_resolved = 0';
      List<dynamic> whereArgs = [itemId, alertType.code];

      if (locationId != null) {
        whereClause += ' AND location_id = ?';
        whereArgs.add(locationId);
      }

      final result = await db.query(
        'inventory_alerts',
        where: whereClause,
        whereArgs: whereArgs,
        limit: 1,
      );

      if (result.isNotEmpty) {
        return InventoryAlert.fromMap(result.first);
      }

      return null;
    } catch (e) {
      LoggingService.error(
        'خطأ في البحث عن التنبيه الموجود',
        category: 'InventoryAlertsService',
        data: {'error': e.toString()},
      );
      return null;
    }
  }
}
